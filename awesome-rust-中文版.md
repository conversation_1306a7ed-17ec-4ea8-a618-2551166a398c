# Awesome Rust 中文版 [![lint badge](https://github.com/rust-unofficial/awesome-rust/actions/workflows/lint.yml/badge.svg)](https://github.com/rust-unofficial/awesome-rust/actions/workflows/lint.yml) [![build badge](https://github.com/rust-unofficial/awesome-rust/actions/workflows/rust.yml/badge.svg?branch=main)](https://github.com/rust-unofficial/awesome-rust/actions/workflows/rust.yml) [![Track Awesome List](https://www.trackawesomelist.com/badge.svg)](https://www.trackawesomelist.com/rust-unofficial/awesome-rust/)

一个精心策划的 Rust 代码和资源列表。

如果您想贡献，请阅读[贡献指南](CONTRIBUTING.md)。

## 目录

- [应用程序](#应用程序)
  * [音频和音乐](#音频和音乐)
  * [区块链](#区块链)
  * [数据库](#数据库)
  * [嵌入式](#嵌入式)
  * [模拟器](#模拟器)
  * [文件管理器](#文件管理器)
  * [金融](#金融)
  * [游戏](#游戏)
  * [图形](#图形)
  * [图像处理](#图像处理)
  * [工业自动化](#工业自动化)
  * [消息队列](#消息队列)
  * [MLOps](#mlops)
  * [可观测性](#可观测性)
  * [操作系统](#操作系统)
  * [包管理器](#包管理器)
  * [支付](#支付)
  * [生产力工具](#生产力工具)
  * [路由协议](#路由协议)
  * [安全工具](#安全工具)
  * [社交网络](#社交网络)
  * [系统工具](#系统工具)
  * [任务调度](#任务调度)
  * [文本编辑器](#文本编辑器)
  * [文本处理](#文本处理)
  * [实用工具](#实用工具)
  * [视频](#视频)
  * [虚拟化](#虚拟化)
  * [Web](#web)
  * [Web 服务器](#web-服务器)
- [开发工具](#开发工具)
- [库](#库)
- [注册表](#注册表)
- [资源](#资源)
- [许可证](#许可证)

## 应用程序

* [alacritty](https://github.com/alacritty/alacritty) - 跨平台、GPU 增强的终端模拟器
* [Arti](https://gitlab.torproject.org/tpo/core/arti) - Tor 的实现（目前是一个不太完整的客户端，但值得关注！）
* [asm-cli-rust](https://github.com/cch123/asm-cli-rust) - 交互式汇编 shell
* [clash-verge-rev/clash-verge-rev](https://github.com/clash-verge-rev/clash-verge-rev) - 基于 tauri 和 rust 的跨平台现代 Clash GUI，支持 Windows、macOS 和 Linux
* [cloudflare/boringtun](https://github.com/cloudflare/boringtun) - 用户空间 WireGuard VPN 实现
* [defguard](https://github.com/defguard/defguard) - 企业开源 SSO 和 WireGuard VPN，支持真正的 2FA/MFA
* [denoland/deno](https://github.com/denoland/deno) - 使用 V8 和 Tokio 构建的安全 JavaScript/TypeScript 运行时
* [doprz/dipc](https://github.com/doprz/dipc) - 使用您喜欢的调色板/主题转换您喜欢的图像和壁纸
* [EasyTier](https://github.com/EasyTier/EasyTier) - 简单、功能齐全的去中心化网状 VPN，支持 WireGuard
* [Edit](https://github.com/microsoft/edit) - 满足简单需求的简单编辑器
* [fcsonline/drill](https://github.com/fcsonline/drill) - 受 Ansible 语法启发的 HTTP 负载测试应用程序
* [fend](https://github.com/printfn/fend) - 任意精度单位感知计算器
* [Fractalide](https://github.com/fractalide/fractalide) - 简单微服务
* [habitat](https://github.com/habitat-sh/habitat) - Chef 创建的用于构建、部署和管理应用程序的工具
* [Herd](https://github.com/imjacobclark/Herd) - 实验性 HTTP 负载测试应用程序
* [hickory-dns](https://crates.io/crates/hickory-dns) - DNS 服务器
* [innernet](https://github.com/tonarino/innernet) - 使用 Wireguard 的覆盖或私有网状网络
* [jedisct1/flowgger](https://github.com/awslabs/flowgger) - 快速、简单、轻量级的数据收集器
* [kalker](https://github.com/PaddiM8/kalker) - 支持类数学语法的科学计算器，支持用户定义变量、函数、求导、积分和复数。跨平台 + WASM 支持
* [kftray](https://github.com/hcavarsan/kftray) - 用于管理和共享多个 kubectl 端口转发配置的跨平台系统托盘应用
* [kytan](https://github.com/changlan/kytan) - 高性能点对点 VPN
* [linkerd/linkerd2-proxy](https://github.com/linkerd/linkerd2-proxy) - Kubernetes 的超轻量级服务网格
* [MaidSafe](https://github.com/maidsafe) - 去中心化平台
* [mdBook](https://github.com/rust-lang/mdBook) - 从 markdown 文件创建书籍的命令行工具
* [Mega](https://github.com/web3infra-foundation/mega) - 支持 Git 的单仓库和单体代码库管理系统，也是 Google Piper 的非官方开源实现
* [mirrord](https://github.com/metalbear-co/mirrord) - 连接您的本地进程和云环境，在云条件下运行本地代码
* [nicohman/eidolon](https://github.com/nicohman/eidolon) - Linux 和 macOS 的 Steam 和无 DRM 游戏注册表和启动器
* [Pijul](https://pijul.org) - 基于补丁的分布式版本控制系统
* [Rauthy](https://github.com/sebadob/rauthy) - OpenID Connect 单点登录身份和访问管理
* [Rio](https://github.com/raphamorim/rio) - 由 WebGPU 驱动的硬件加速 GPU 终端模拟器，专注于在桌面和浏览器中运行
* [Rust Iot Platform](https://github.com/iot-ecology/rust-iot-platform) - 使用 Rust 构建的高性能物联网开发平台，专为多协议支持和实时数据处理而设计
* [rx](https://github.com/cloudhead/rx) - Vi 风格的现代像素艺术编辑器
* [Ryot](https://github.com/ignisda/ryot) - 自托管应用程序，用于跟踪媒体消费、健身等
* [Saga Reader](https://github.com/sopaco/saga-reader) - 由 AI 驱动的极速轻量级互联网阅读器，支持搜索引擎信息获取和 RSS
* [Servo](https://github.com/servo/servo) - 原型 Web 浏览器引擎
* [shoes](https://github.com/cfal/shoes) - 多协议代理服务器
* [shuttle](https://github.com/shuttle-hq/shuttle) - 无服务器平台
* [Sniffnet](https://github.com/GyulyVGC/sniffnet) - 轻松监控网络流量的跨平台应用程序
* [SWC](https://github.com/swc-project/swc) - 超快速 TypeScript / JavaScript 编译器
* [tiny](https://github.com/osa1/tiny) - 终端 IRC 客户端
* [UpVPN](https://github.com/upvpn/upvpn-app) - 基于 Tauri 构建的 macOS、Linux 和 Windows WireGuard VPN 客户端
* [vproxy](https://github.com/0x676e67/vproxy) - 高性能 HTTP/HTTPS/SOCKS5 代理服务器
* [wasmer](https://github.com/wasmerio/wasmer) - 支持 WASI 和 Emscripten 的安全快速 WebAssembly 运行时
* [Weld](https://github.com/serayuzgur/weld) - 完整的假 REST API 生成器
* [wezterm](https://github.com/wezterm/wezterm) - GPU 加速的跨平台终端模拟器和多路复用器
* [WinterJS](https://github.com/wasmerio/winterjs) - 使用 SpiderMonkey 和 Axum 构建的安全 JavaScript 运行时
* [zellij](https://github.com/zellij-org/zellij) - 内置电池的终端多路复用器（工作区）

### 音频和音乐

* [dano](https://github.com/kimono-koans/dano) - 媒体文件的 hashdeep/md5tree（但功能更多）
* [enginesound](https://github.com/DasEtwas/enginesound) - 用于程序化生成半真实引擎声音的 GUI 和命令行应用程序
* [Festival](https://github.com/hinto-janai/festival) - 本地音乐播放器/服务器/客户端
* [figsoda/mmtc](https://github.com/figsoda/mmtc) - 最小化的 mpd 终端客户端，旨在简单但高度可配置
* [Glicol](https://github.com/chaosprint/glicol) - 面向图形的实时编码语言，用于浏览器中的协作音乐制作
* [ncspot](https://github.com/hrkfdn/ncspot) - 跨平台 ncurses Spotify 客户端，受 ncmpc 等启发
* [Pinepods](https://github.com/madeofpendletonwool/PinePods) - 基于 Rust 的多用户播客管理系统
* [Polaris](https://github.com/agersant/polaris) - 音乐流媒体应用程序
* [Spotify Player](https://github.com/aome510/spotify-player) - 终端中的 Spotify 播放器，功能完整
* [Spotifyd](https://github.com/Spotifyd/spotifyd) - 作为 UNIX 守护进程运行的开源 Spotify 客户端
* [termusic](https://github.com/tramhao/termusic) - 用 Rust 编写的音乐播放器 TUI
* [WhatBPM](https://github.com/sergree/whatbpm) - 为电子舞曲制作人提供的每日静态生成信息资源

### 区块链

* [Anchor](https://github.com/solana-foundation/anchor) - 构建安全 Solana 程序（智能合约）的领先开发框架
* [artemis](https://github.com/paradigmxyz/artemis) - 编写 MEV 机器人的简单、模块化、快速框架
* [beerus](https://github.com/eigerco/beerus) - 无信任的 StarkNet 轻客户端，⚡极速⚡
* [Bitcoin Satoshi's Vision](https://github.com/brentongunning/rust-sv) - 用于处理 Bitcoin SV 的库
* [cairo](https://github.com/starkware-libs/cairo) - 第一个用于通用计算创建可证明程序的图灵完备语言
* [cairo-vm](https://github.com/lambdaclass/cairo-vm) - Cairo VM 的实现
* [ChainX](https://github.com/chainx-org/ChainX) - Polkadot 上完全去中心化的跨链加密资产管理
* [CITA](https://github.com/citahub/cita) - 面向企业用户的高性能区块链内核
* [coinbase-pro-rs](https://github.com/inv2004/coinbase-pro-rs) - Coinbase pro 客户端，支持同步/异步/websocket
* [Diem](https://github.com/diem/diem) - Diem 的使命是实现简单的全球货币和金融基础设施
* [dusk-network/rusk](https://github.com/dusk-network/rusk) - Dusk 的参考实现，专注于隐私的可扩展 FMI
* [electrumrs](https://github.com/romanz/electrs) - Electrum 服务器的高效重新实现
* [ethabi](https://github.com/rust-ethereum/ethabi) - 编码和解码智能合约调用
* [ethaddrgen](https://github.com/Limeth/ethaddrgen) - 自定义以太坊虚荣地址生成器
* [etk](https://github.com/quilt/etk) - 用于编写、读取和分析 EVM 字节码的工具集合
* [Forest](https://github.com/ChainSafe/forest) - Filecoin 实现
* [Foundry](https://github.com/foundry-rs/foundry) - 用于以太坊应用程序开发的极速、便携、模块化工具包
* [Grin](https://github.com/mimblewimble/grin/) - MimbleWimble 协议的演进
* [hdwallet](https://github.com/jjyr/hdwallet) - BIP-32 HD 钱包相关密钥派生工具
* [Holochain](https://github.com/holochain/holochain) - 区块链的可扩展 P2P 替代方案
* [Hyperlane](https://github.com/hyperlane-xyz/hyperlane-monorepo) - 无许可、模块化互操作性框架
* [ibc-rs](https://github.com/informalsystems/hermes) - 跨区块链通信协议的实现
* [infincia/bip39-rs](https://github.com/infincia/bip39-rs) - BIP39 的实现
* [interBTC](https://github.com/interlay/interbtc) - 到 Polkadot 和 Kusama 的无信任完全去中心化比特币桥
* [Joystream](https://github.com/Joystream/joystream) - 用户治理的视频平台
* [Kaspa](https://github.com/kaspanet/rusty-kaspa) - 世界上最快、开源、去中心化且完全可扩展的第一层
* [Lighthouse](https://github.com/sigp/lighthouse) - 以太坊共识层（CL）客户端
* [near/nearcore](https://github.com/near/nearcore) - 面向低端移动设备的去中心化智能合约平台
* [Nervos CKB](https://github.com/nervosnetwork/ckb) - Nervos CKB 是一个公共无许可区块链
* [opensea-rs](https://github.com/gakonst/opensea-rs) - Opensea API 和合约的绑定和 CLI
* [Parity-Bitcoin](https://github.com/paritytech/parity-bitcoin) - Parity 比特币客户端
* [Phala-Network/phala-blockchain](https://github.com/Phala-Network/phala-blockchain) - 基于 Intel SGX 和 Substrate 的机密智能合约区块链
* [polkadot-sdk](https://github.com/paritytech/polkadot-sdk) - Parity Polkadot 区块链 SDK
* [reth](https://github.com/paradigmxyz/reth) - 以太坊协议的模块化、贡献者友好、极速实现
* [revm](https://github.com/bluealloy/revm) - 革命性机器（revm）是一个快速的以太坊虚拟机
* [rust-bitcoin](https://github.com/rust-bitcoin/rust-bitcoin) - 支持比特币相关数据结构和网络消息的反序列化、解析和执行的库
* [rust-lightning](https://github.com/lightningdevkit/rust-lightning) - 比特币闪电网络库
* [sigma-rust](https://github.com/ergoplatform/sigma-rust) - ErgoTree 解释器和钱包相关功能
* [Subspace](https://github.com/autonomys/subspace) - 第一个能够完全解决区块链三难问题的第一层区块链
* [Sui](https://github.com/MystenLabs/sui) - 下一代智能合约平台，具有高吞吐量、低延迟
* [svm-rs](https://github.com/alloy-rs/svm-rs) - Solidity 编译器版本管理器
* [tendermint-rs](https://github.com/cometbft/tendermint-rs) - Tendermint 区块链数据结构和客户端
* [wagyu](https://github.com/howardwu/wagyu) - 生成加密货币钱包的库
* [zcash](https://github.com/zcash/zcash) - Zcash 是"Zerocash"协议的实现

### 数据库

* [apecloud/ape-dts](https://github.com/apecloud/ape-dts) - 数据传输套件。提供 MySQL、PostgreSQL、Redis、MongoDB、Kafka、ClickHouse 等之间的数据复制
* [Atomic-Server](https://github.com/atomicdata-dev/atomic-server/) - 具有实时更新、动态索引和易于使用的 GUI 的 NoSQL 图数据库
* [CozoDB](https://github.com/cozodb/cozo) - 使用 Datalog 并专注于图数据和算法的事务性关系数据库
* [darkbird](https://github.com/Rustixir/darkbird) - 受 erlang mnesia 启发的高并发、实时、内存存储
* [Databend](https://github.com/databendlabs/databend) - 具有云原生架构的现代实时数据处理和分析 DBMS
* [DB3 Network](https://github.com/dbpunk-labs/db3) - DB3 是社区驱动的区块链第二层去中心化数据库网络
* [erikgrinaker/toydb](https://github.com/erikgrinaker/toydb) - 分布式 SQL 数据库，作为学习项目编写
* [Garage](https://github.com/deuxfleurs-org/garage) - 为中小规模自托管设计的 S3 兼容分布式对象存储服务
* [GlueSQL](https://github.com/gluesql/gluesql) - SQL 数据库的 Rust 库，包括解析器、执行层和各种存储选项
* [GreptimeDB](https://github.com/grepTimeTeam/greptimedb/) - 支持 PromQL/SQL/Python 的开源云原生分布式时序数据库
* [HelixDB](https://github.com/HelixDB/helix-db) - 用于 RAG 和 AI 智能数据存储的强大图向量数据库
* [Hiqlite](https://github.com/sebadob/hiqlite) - 高可用、可嵌入、基于 raft 的 SQLite + 缓存
* [indradb](https://crates.io/crates/indradb) - 图数据库
* [KiteSQL](https://github.com/KipData/KiteSQL) - SQL 作为 Rust 的函数
* [lancedb](https://github.com/lancedb/lancedb) - 用于 AI 应用的无服务器、低延迟向量数据库
* [Lucid](https://github.com/lucid-kv/lucid) - 通过 HTTP API 访问的高性能分布式 KV 存储
* [Materialize](https://github.com/MaterializeInc/materialize) - 由 Timely Dataflow 驱动的流式 SQL 数据库
* [native_db](https://github.com/vincent-herlemont/native_db) - 多平台应用的嵌入式数据库（服务器、桌面、移动）
* [Neon](https://github.com/neondatabase/neon) - 无服务器 Postgres。我们分离了存储和计算以提供自动扩展、分支和无底存储
* [noria](https://github.com/mit-pdos/noria) - 为 Web 应用后端设计的动态变化、部分有状态数据流
* [oxigraph/oxigraph](https://github.com/oxigraph/oxigraph) - 实现 SPARQL 标准的图数据库
* [ParadeDB](https://github.com/paradedb/paradedb/) - 基于 Postgres 构建的 Elasticsearch 替代方案，专为实时搜索和分析而设计
* [ParityDB](https://github.com/paritytech/parity-db) - 针对读操作优化的快速可靠数据库
* [PumpkinDB](https://github.com/PumpkinDB/PumpkinDB) - 事件溯源数据库引擎
* [Qdrant](https://github.com/qdrant/qdrant) - 具有扩展过滤支持的开源向量相似性搜索引擎
* [Qrlew/qrlew](https://github.com/Qrlew/qrlew) - SQL 到 SQL 差分隐私层
* [RisingWaveLabs/RisingWave](https://github.com/RisingWaveLabs/risingwave) - 云中的下一代流数据库
* [seppo0010/rsedis](https://github.com/seppo0010/rsedis) - Redis 重新实现
* [Skytable](https://github.com/skytable/skytable) - 多模型 NoSQL 数据库
* [sled](https://crates.io/crates/sled) - （测试版）现代嵌入式数据库
* [SQLSync](https://github.com/orbitinghail/sqlsync) - 多人离线优先 SQLite
* [SurrealDB](https://github.com/surrealdb/surrealdb) - 可扩展、分布式、文档图数据库
* [TerminusDB](https://github.com/terminusdb/terminusdb-store) - 开源图数据库和文档存储
* [tikv](https://github.com/tikv/tikv) - Rust 中的分布式 KV 数据库
* [Tonbo](https://github.com/tonbo-io/tonbo) - 基于 Apache Arrow 和 Parquet 构建的嵌入式持久数据库
* [TrailBase](https://github.com/trailbaseio/trailbase) - 快速、轻量级、单文件 FireBase 替代方案
* [Turso](https://github.com/tursodatabase/turso) - Turso 数据库是进程内 SQL 数据库，与 SQLite 兼容
* [USearch](https://github.com/unum-cloud/usearch) - 向量和字符串的相似性搜索引擎
* [valentinus](https://github.com/kn0sys/valentinus) - 使用 LMDB 绑定构建的下一代向量数据库
* [vorot93/libmdbx-rs](https://github.com/vorot93/libmdbx-rs) - MDBX 的绑定，一个"快速、紧凑、强大、嵌入式、事务性键值数据库"
* [WooriDB](https://github.com/naomijub/wooridb) - 受 Crux 和 Datomic 启发的通用时间序列数据库

### 嵌入式

* [rmk](https://github.com/haobogu/rmk) - 功能丰富的键盘固件
* [uefi-rs](https://github.com/rust-osdev/uefi-rs) - 统一可扩展固件接口的 Rust 包装器

### 模拟器

另请参阅[匹配关键字 'emulator' 的 crates](https://crates.io/keywords/emulator)。

* CHIP-8
  * [ColinEberhardt/wasm-rust-chip8](https://github.com/ColinEberhardt/wasm-rust-chip8) - WebAssembly CHIP-8 模拟器
  * [starrhorne/chip8-rust](https://github.com/starrhorne/chip8-rust) - chip8 模拟器
* Commodore 64
  * [kondrak/rust64](https://github.com/kondrak/rust64) - Commodore 64 模拟器
* Flash Player
  * [Ruffle](https://github.com/ruffle-rs/ruffle) - Adobe Flash Player 模拟器，目标是桌面和使用 WebAssembly 的 Web
* Game Boy Advance
  * [michelhe/rustboyadvance-ng](https://github.com/michelhe/rustboyadvance-ng) - RustBoyAdvance-ng 是一个 Game Boy Advance 模拟器和调试器
* GameMaker
  * [OpenGMK](https://github.com/OpenGMK/OpenGMK) - OpenGMK 是 GameMaker Classic 引擎的现代重写
* Intel 8080 CPU
  * [mohanson/i8080](https://github.com/mohanson/i8080) - Intel 8080 CPU 模拟器
* iPod
  * [clicky-pen/clickwheel-ipod](https://github.com/clicky-pen/clickwheel-ipod) - 点击轮 iPod 模拟器（WIP）
* NES
  * [koute/pinky](https://github.com/koute/pinky) - NES 模拟器
  * [pcwalton/sprocketnes](https://github.com/pcwalton/sprocketnes) - NES 模拟器
* Nintendo 64
  * [gopher64/gopher64](https://github.com/gopher64/gopher64) - N64 模拟器
* Nintendo DS
  * [dust](https://github.com/kelpsyberry/dust) - Nintendo DS 模拟器
* PlayStation 4
  * [Obliteration](https://github.com/obhq/obliteration) - 实验性 PS4 模拟器
* ZX Spectrum
  * [rustzx/rustzx](https://github.com/rustzx/rustzx) - ZX Spectrum 模拟器

### 文件管理器

* [broot](https://github.com/Canop/broot) - 查看和导航目录树的新方法
* [felix](https://github.com/kyoheiu/felix) - tui 文件管理器，具有 vim 风格的键绑定
* [joshuto](https://github.com/kamiyaa/joshuto) - 受 ranger 启发的终端文件管理器
* [lf](https://github.com/gokcehan/lf) - 终端文件管理器
* [nnn](https://github.com/jarun/nnn) - 最快的终端文件管理器
* [ranger](https://github.com/ranger/ranger) - 具有 VI 键绑定的控制台文件管理器
* [xplr](https://github.com/sayanarijit/xplr) - 可破解、最小、快速的 TUI 文件浏览器
* [yazi](https://github.com/sxyazi/yazi) - 极速终端文件管理器，基于异步 I/O

### 金融

* [GreptimeTeam/greptimedb](https://github.com/GreptimeTeam/greptimedb) - 开源、云原生、分布式时序数据库，支持 PromQL/SQL/Python
* [Kurtosis](https://github.com/kurtosis-tech/kurtosis) - 用于多容器测试环境的平台
* [Meilisearch](https://github.com/meilisearch/meilisearch) - 超相关、即时、容错的全文搜索 API
* [Polars](https://github.com/pola-rs/polars) - 快速多线程、混合内存外查询引擎，专注于 DataFrame 前端
* [Qdrant](https://github.com/qdrant/qdrant) - 具有扩展过滤支持的开源向量相似性搜索引擎
* [Tantivy](https://github.com/quickwit-oss/tantivy) - 用 Rust 编写的马速全文搜索引擎库

### 游戏

另请参阅[游戏开发](#游戏开发)和[游戏引擎](#游戏引擎)。

* [citybound](https://github.com/citybound/citybound) - 您应得的城市模拟
* [cristicbz/rust-doom](https://github.com/cristicbz/rust-doom) - Doom 的渲染器，可能会发展成为一个可玩的游戏
* [doukutsu-rs](https://github.com/doukutsu-rs/doukutsu-rs) - Cave Story 引擎的重新实现
* [garkimasera/rusted-ruins](https://github.com/garkimasera/rusted-ruins) - 可扩展的开放世界 rogue like 游戏，具有像素艺术
* [gorilla-devs/ferium](https://github.com/gorilla-devs/ferium) - Ferium 是一个快速且功能丰富的跨平台 Minecraft mod 管理器
* [lifthrasiir/angolmois-rust](https://github.com/lifthrasiir/angolmois-rust) - 支持 BMS 格式的简约音乐视频游戏
* [maras-archive/rsnake](https://github.com/maras-archive/rsnake) - 用 Rust 编写的贪吃蛇
* [mtkennerly/ludusavi](https://github.com/mtkennerly/ludusavi) - 视频游戏存档的备份工具
* [ozkriff/zemeroth](https://github.com/ozkriff/zemeroth) - 小型 2D 回合制六角战略游戏
* [rhex](https://github.com/dpc/rhex) - 六角 ascii roguelike
* [rsaarelm/magog](https://github.com/rsaarelm/magog) - Rust 中的 roguelike 游戏
* [SoftbearStudios/mk48](https://github.com/SoftbearStudios/mk48) - Mk48.io 是一个在线多人海军战斗游戏
* [swatteau/sokoban-rs](https://github.com/swatteau/sokoban-rs) - Sokoban 实现
* [thetawavegame/thetawave](https://github.com/thetawavegame/thetawave) - 太空射击游戏，具有程序生成的关卡、物理、着色器和声音
* [Thinkofname/rust-quake](https://github.com/Thinkofname/rust-quake) - Quake 地图渲染器
* [ttyperacer/terminal-typeracer](https://github.com/ttyperacer/terminal-typeracer) - 单人终端打字游戏
* [Veloren](https://gitlab.com/veloren/veloren) - 开放世界、开源多人体素 RPG，目前处于 alpha 开发阶段
* [Zone of Control](https://github.com/ozkriff/zoc) - 回合制六角战略游戏

### 图形

* [dps/rust-raytracer](https://github.com/dps/rust-raytracer) - 非常简单的光线追踪器的实现
* [ivanceras/svgbob](https://github.com/ivanceras/svgbob) - 将 ASCII 图表转换为 SVG 图形
* [KaminariOS/rustracer](https://github.com/KaminariOS/rustracer) - PBR 光线追踪器和实用程序，用于 glTF 2.0
* [Limeth/euclider](https://github.com/Limeth/euclider) - 实时 4D CPU 光线追踪器
* [mikigraf/Image-Processing-CLI-in-Rust](https://github.com/mikigraf/Image-Processing-CLI-in-Rust) - CLI，用于处理图像，生成直方图
* [RazrFalcon/resvg](https://github.com/RazrFalcon/resvg) - SVG 渲染库
* [rodrigorc/papercraft](https://github.com/rodrigorc/papercraft) - 用于展开 3D 模型并创建纸工艺品的工具
* [rustq/vue-skia](https://github.com/rustq/vue-skia) - Skia 基于 2D 图形 vue 渲染库。它基于 Rust 来实现软件光栅化以提高性能
* [turnage/valora](https://github.com/turnage/valora) - 生成美术库
* [wahn/rs_pbrt](https://github.com/wahn/rs_pbrt) - 实现基于物理的渲染器，专注于 C++ 版本的正确性

### 图像处理

* [Aloxaf/silicon](https://github.com/Aloxaf/silicon) - 从源代码创建美丽的图像
* [twistedfall/opencv-rust](https://github.com/twistedfall/opencv-rust) - OpenCV 的 Rust 绑定

### 工业自动化

* [locka99/opcua](https://github.com/locka99/opcua) - 纯 Rust OPC UA 库
* [slowtec/tokio-modbus](https://github.com/slowtec/tokio-modbus) - tokio 的 Modbus 库

### 消息队列

* [apache/rocketmq-rust](https://github.com/apache/rocketmq-rust) - RocketMQ Rust 客户端
* [iggy-rs/iggy](https://github.com/iggy-rs/iggy) - 持久消息流平台，用 Rust 编写，支持 QUIC、TCP 和 HTTP 传输协议，具有非常高的性能
* [launchbadge/sqlx](https://github.com/launchbadge/sqlx) - 异步 PostgreSQL/MySQL/SQLite 连接池，具有强类型支持

### MLOps

* [candle-transformers](https://github.com/huggingface/candle) - 极简 ML 框架
* [linfa](https://github.com/rust-ml/linfa) - 机器学习工具包
* [smartcore](https://github.com/smartcorelib/smartcore) - 机器学习库

### 可观测性

* [openobserve](https://github.com/openobserve/openobserve) - 10 倍更容易，140 倍更低的存储成本，高性能，PB 级 - Elasticsearch/Splunk/Datadog 替代方案
* [quickwit-oss/quickwit](https://github.com/quickwit-oss/quickwit) - 云原生搜索引擎
* [vectordotdev/vector](https://github.com/vectordotdev/vector) - 高性能可观测性数据管道

### 操作系统

另请参阅[用 Rust 编写的操作系统比较](https://github.com/flosse/rust-os-comparison)。

* [0x59616e/SteinsOS](https://github.com/0x59616e/SteinsOS) - 用 Rust 编写的 OS
* [Andy-Python-Programmer/aero](https://github.com/Andy-Python-Programmer/aero) - 遵循单内核设计的现代、实验性、类 UNIX 操作系统
* [DragonOS-Community/DragonOS](https://github.com/DragonOS-Community/DragonOS) - 用 Rust 从头开始编写的操作系统，具有 Linux 兼容性
* [redox-os/redox](https://gitlab.redox-os.org/redox-os/redox) - 用 Rust 编写的操作系统
* [thepowersgang/rust_os](https://github.com/thepowersgang/rust_os) - 用 Rust 编写的 OS 内核。非 POSIX
* [theseus-os/Theseus](https://github.com/theseus-os/Theseus) - 用 Rust 从头开始编写的安全语言、单地址空间和单特权级操作系统
* [tock/tock](https://github.com/tock/tock) - 基于 Cortex-M 的微控制器的安全嵌入式操作系统

### 包管理器

* [helsing-ai/buffrs](https://github.com/helsing-ai/buffrs) - 现代 Protocol Buffers 包管理器
* [prefix-dev/pixi](https://github.com/prefix-dev/pixi) - 包管理变得简单

### 支付

* [hyperswitch](https://github.com/juspay/hyperswitch) - 开源支付交换机，用 Rust 编写，让支付变得快速、可靠且价格合理

### 生产力工具

* [ast-grep](https://github.com/ast-grep/ast-grep) - 用于代码结构搜索、lint 和重写的 CLI 工具
* [espanso](https://github.com/espanso/espanso) - 跨平台文本扩展器
* [eureka](https://github.com/simeg/eureka) - CLI 工具，无需离开终端即可输入和存储您的想法
* [Furtherance](https://github.com/lakoliu/Furtherance) - 用 Rust 和 GTK4 构建的时间跟踪应用程序
* [illacloud/illa](https://github.com/illacloud/illa) - 低代码内部工具构建器
* [LLDAP](https://github.com/lldap/lldap) - 简化的 LDAP 接口，用于身份验证
* [organicmaps/organicmaps](https://github.com/organicmaps/organicmaps) - 离线地图应用程序，适用于旅行者、游客、徒步旅行者和骑自行车者
* [pier-cli/pier](https://github.com/pier-cli/pier) - 用于管理（添加、搜索元数据等）所有单行代码、脚本、工具和 CLI 的中央存储库
* [ShadoySV/work-break-balance](https://github.com/ShadoySV/work-break-balance) - 简单的工作-休息平衡应用程序

### 路由协议

* [Holo](https://github.com/holo-routing/holo) - Holo 是一套路由协议，设计用于支持高规模和自动化驱动的网络
* [RustyBGP](https://github.com/osrg/rustybgp) - BGP 实现

### 安全工具

* [AFLplusplus/LibAFL](https://github.com/AFLplusplus/LibAFL) - 高级模糊测试库 - 插槽您自己的模糊测试器
* [arvancloud/libinjection-rs](https://github.com/arvancloud/libinjection-rs) - libinjection 的 Rust 绑定
* [cargo-audit](https://github.com/RustSec/rustsec/tree/main/cargo-audit) - 审计 Cargo.lock 文件中的已知安全漏洞
* [cargo-auditable](https://github.com/rust-secure-code/cargo-auditable) - 使生产 Rust 二进制文件可审计
* [cargo-deny](https://github.com/EmbarkStudios/cargo-deny) - Cargo 插件，帮助您管理大型依赖图
* [Cherrybomb](https://github.com/blst-security/cherrybomb) - 使用 CLI 工具停止半生不熟的 API
* [cotp](https://github.com/replydev/cotp) - 值得信赖、加密的、命令行 TOTP/HOTP 身份验证器应用程序，具有导入功能
* [epi052/feroxbuster](https://github.com/epi052/feroxbuster) - 简单、快速、递归的内容发现工具
* [Inspektor](https://github.com/psiinon/inspektor) - 侧重于可用性和图形的命令行动态检测工具
* [kpcyrd/authoscope](https://github.com/kpcyrd/authoscope) - 可编写脚本的网络身份验证破解器
* [kpcyrd/rshijack](https://github.com/kpcyrd/rshijack) - TCP 连接劫持器
* [kpcyrd/sn0int](https://github.com/kpcyrd/sn0int) - 半自动 OSINT 框架和包管理器
* [kpcyrd/sniffglue](https://github.com/kpcyrd/sniffglue) - 安全的多线程数据包嗅探器
* [ObserverWard](https://github.com/0x727/ObserverWard) - 跨平台社区 Web 指纹识别工具
* [ripasso](https://github.com/cortex/ripasso/) - 密码管理器，与 pass 兼容
* [rustscan/rustscan](https://github.com/RustScan/RustScan) - 让 Nmap 更快
* [sqlx-cli](https://github.com/launchbadge/sqlx/tree/main/sqlx-cli) - 离线 SQL 查询检查

### 社交网络

* [Aardwolf](https://github.com/Aardwolf-Social/aardwolf) - Facebook 的联合替代方案
* [damus](https://github.com/damus-io/damus) - iOS 的社交网络应用程序
* [Lemmy](https://github.com/LemmyNet/lemmy) - 联邦宇宙的链接聚合器/Reddit 克隆
* [Mastodon](https://github.com/mastodon/mastodon) - 您自己的社交网络服务器
* [Plume](https://github.com/Plume-org/Plume) - 联合博客引擎

### 系统工具

* [ajeetdsouza/zoxide](https://github.com/ajeetdsouza/zoxide) - 学习您习惯的 cd 命令的快速替代方案
* [bandwhich](https://github.com/imsnif/bandwhich) - 终端带宽利用工具
* [bottom](https://github.com/ClementTsang/bottom) - 另一个跨平台图形进程/系统监视器
* [brocode/fblog](https://github.com/brocode/fblog) - 小型命令行 JSON 日志查看器
* [broot](https://github.com/Canop/broot) - 查看和导航目录树的新方法
* [bustd](https://github.com/vrmiguel/bustd) - 轻量级进程杀手守护进程，用于处理 Linux 上的内存不足情况
* [cantino/mcfly](https://github.com/cantino/mcfly) - 通过神经网络飞过您的 shell 历史记录
* [crabz](https://github.com/sstadick/crabz) - 多线程压缩和解压缩 CLI 工具
* [cristianoliveira/funzzy](https://github.com/cristianoliveira/funzzy) - 受 entr 启发的可配置文件系统观察器
* [dalance/procs](https://github.com/dalance/procs) - 'ps' 的现代替代品
* [ddh](https://github.com/darakian/ddh) - 快速重复文件查找器
* [diskonaut](https://github.com/imsnif/diskonaut) - 终端磁盘空间导航器
* [dust](https://github.com/bootandy/dust) - du 的更直观版本
* [eza](https://github.com/eza-community/eza) - ls 的替代品
* [fd](https://github.com/sharkdp/fd) - find 的简单、快速和用户友好的替代品
* [fselect](https://github.com/jhspetersson/fselect) - 使用类似 SQL 的查询查找文件
* [gitui](https://github.com/extrawurst/gitui) - 用 Rust 编写的 git 的极速终端 UI
* [gping](https://github.com/orf/gping) - 带有图形的 ping，但在终端中
* [grex](https://github.com/pemistahl/grex) - 用于从用户提供的测试用例生成正则表达式的命令行工具和库
* [hyperfine](https://github.com/sharkdp/hyperfine) - 命令行基准测试工具
* [just](https://github.com/casey/just) - 用于保存和运行项目特定命令的便捷命令运行器
* [kalker](https://github.com/PaddiM8/kalker) - 科学计算器，支持类数学语法，具有用户定义变量、函数、求导、积分和复数
* [kmon](https://github.com/orhun/kmon) - Linux 内核管理器和活动监视器
* [lsd](https://github.com/lsd-rs/lsd) - 下一代 ls 命令
* [macchina](https://github.com/Macchina-CLI/macchina) - 快速、最小且高度可定制的系统信息前端
* [mdcat](https://github.com/swsnr/mdcat) - cat for markdown
* [mprocs](https://github.com/pvolok/mprocs) - 在一个终端中运行多个命令
* [nushell/nushell](https://github.com/nushell/nushell) - 新型 shell
* [orhun/kmon](https://github.com/orhun/kmon) - Linux 内核管理器和活动监视器
* [ouch](https://github.com/ouch-org/ouch) - 无痛压缩和解压缩
* [pueue](https://github.com/Nukesor/pueue) - 管理长时间运行的 shell 命令
* [ripgrep](https://github.com/BurntSushi/ripgrep) - 递归搜索目录以查找正则表达式模式，同时尊重您的 gitignore
* [sd](https://github.com/chmln/sd) - 直观的查找和替换 CLI（sed 替代品）
* [skim](https://github.com/lotabout/skim) - 纯 Rust 中的模糊查找器
* [starship](https://github.com/starship/starship) - 适用于任何 shell 的最小、极快且高度可定制的提示符
* [tealdeer](https://github.com/dbrgn/tealdeer) - 非常快速的 tldr 客户端实现
* [tokei](https://github.com/XAMPPRocky/tokei) - 计算代码行数，快速
* [topgrade](https://github.com/topgrade-rs/topgrade) - 升级系统中的所有内容
* [trippy](https://github.com/fujiapple852/trippy) - 网络诊断工具
* [watchexec](https://github.com/watchexec/watchexec) - 执行命令以响应文件修改
* [xh](https://github.com/ducaale/xh) - 友好且快速的发送 HTTP 请求工具
* [zellij](https://github.com/zellij-org/zellij) - 终端工作区，内置电池

### 任务调度

* [delay-timer](https://github.com/BinChengZhao/delay-timer) - 延迟任务的时间管理器。类似 crontab，但可以执行异步任务
* [persistent-scheduler](https://github.com/rustmailer/persistent-scheduler) - 使用 Tokio 构建的高性能任务调度系统

### 文本编辑器

* [amp](https://github.com/jmacdonald/amp) - 受 Vi/Vim 启发
* [emacs-ng](https://github.com/emacs-ng/emacs-ng) - 使用 Rust 重新实现 Emacs
* [gchp/iota](https://github.com/gchp/iota) - 简单的文本编辑器
* [helix-editor/helix](https://github.com/helix-editor/helix) - 后现代模态文本编辑器
* [ilai-deutel/kibi](https://github.com/ilai-deutel/kibi) - 具有语法高亮、增量搜索等功能的小型（<1024 LOC）文本编辑器
* [lapce/lapce](https://github.com/lapce/lapce) - 用 Rust 编写的现代编辑器
* [mathall/rim](https://github.com/mathall/rim) - 用 Rust 编写的类似 Vim 的文本编辑器
* [ox](https://github.com/curlpipe/ox) - 在终端中运行的独立 Rust 文本编辑器！
* [vamolessa/pepper](https://github.com/vamolessa/pepper) - 一个自以为是的模态编辑器，用于简化从终端进行的代码编辑
* [xi-editor](https://github.com/xi-editor/xi-editor) - 现代编辑器，后端用 Rust 编写

### 文本处理

* [becheran/wildmatch](https://github.com/becheran/wildmatch) - 带有问号和星号通配符操作符的简单字符串匹配
* [BurntSushi/suffix](https://github.com/BurntSushi/suffix) - 线性时间后缀数组构造（支持 Unicode）
* [BurntSushi/tabwriter](https://github.com/BurntSushi/tabwriter) - 弹性制表位（即文本列对齐）
* [cpc](https://github.com/probablykasper/cpc) - 解析和计算数学字符串，支持单位和单位转换
* [Daniel-Liu-c0deb0t/triple_accel](https://github.com/Daniel-Liu-c0deb0t/triple_accel) - 使用 SIMD 加速的 Rust 编辑距离例程
* [fancy-regex/fancy-regex](https://github.com/fancy-regex/fancy-regex) - 正则表达式实现，支持相对丰富的功能集
* [greyblake/whatlang-rs](https://github.com/greyblake/whatlang-rs) - 基于三元组的自然语言检测库
* [Lucretiel/joinery](https://github.com/Lucretiel/joinery) - 通用字符串 + 可迭代连接
* [mgeisler/textwrap](https://github.com/mgeisler/textwrap) - 文本换行（支持连字符）
* [null8626/decancer](https://github.com/null8626/decancer) - 从字符串中删除常见 unicode 混淆字符/同形字符的小包
* [ps1dr3x/easy_reader](https://github.com/ps1dr3x/easy_reader) - 允许在巨大文件的行中前进、后退和随机导航的阅读器
* [pwoolcoc/ngrams](https://github.com/pwoolcoc/ngrams) - 从任意迭代器构造 n-grams
* [rust-lang/regex](https://github.com/rust-lang/regex) - 正则表达式（RE2 风格）
* [strsim-rs](https://crates.io/crates/strsim) - 字符串相似性度量
* [yaa110/rake-rs](https://github.com/yaa110/rake-rs) - RAKE 算法的多语言实现

### 实用工具

* [1History](https://github.com/1History/1History) - 命令行界面，用于备份 Firefox/Chrome/Safari 历史记录到一个或多个 git 存储库
* [aleshaleksey/AZDice](https://github.com/aleshaleksey/AZDice) - 成功/失败骰子系统的骰子滚动器
* [arthrp/consoletimer](https://github.com/arthrp/consoletimer) - 终端的简单计时器
* [ashleygwilliams/cargo-generate](https://github.com/ashleygwilliams/cargo-generate) - 通过利用预先存在的 git 存储库作为模板来生成 rust 项目
* [cargo-workspaces](https://github.com/pksunkara/cargo-workspaces) - 用于管理 cargo 工作区和其中的 crate 的工具
* [casey/just](https://github.com/casey/just) - 用于保存和运行项目特定命令的便捷命令运行器
* [clippy](https://crates.io/crates/clippy) - Rust linter
* [clog-tool/clog-cli](https://github.com/clog-tool/clog-cli) - 从 git 元数据生成变更日志
* [create-rust-app](https://github.com/Wulf/create-rust-app) - 通过运行一个命令设置现代 rust+react web 应用程序
* [dan-t/rusty-tags](https://github.com/dan-t/rusty-tags) - 为 cargo 项目及其所有依赖项创建 ctags/etags
* [datanymizer/datanymizer](https://github.com/datanymizer/datanymizer) - 强大的数据库匿名器，具有灵活的规则
* [dprint](https://github.com/dprint/dprint) - 可插拔且可配置的代码格式化平台
* [envio-cli/envio](https://github.com/envio-cli/envio) - 现代且安全的 CLI 工具，用于管理环境变量
* [fcsonline/tmux-thumbs](https://github.com/fcsonline/tmux-thumbs) - 用 Rust 编写的 tmux-fingers 的闪电般快速版本，复制/粘贴 tmux 类似 vimium/vimperator
* [Geekbruce/qrscan](https://github.com/Geekbruce/qrscan) - 扫描媒体文件以查找 QR 码并返回其内容的 CLI 工具
* [guoxbin/dtool](https://github.com/guoxbin/dtool) - 有用的命令行工具集合，用于协助开发
* [hotg-ai/rune](https://github.com/hotg-ai/rune) - WebAssembly 的机器学习推理引擎
* [Kondo](https://github.com/tbillington/kondo) - 用于删除软件项目工件和回收磁盘空间的 CLI 和 GUI 工具
* [kyoheiu/felix](https://github.com/kyoheiu/felix) - tui 文件管理器，具有 vim 风格的键绑定
* [lavifb/todo_r](https://github.com/lavifb/todo_r) - 使用一个命令查找所有 TODO 注释！
* [Linus-Mussmaecher/rucola](https://github.com/Linus-Mussmaecher/rucola) - 终端的基于 Rust 的命令行计算器
* [lotabout/rargs](https://github.com/lotabout/rargs) - xargs + awk，支持模式匹配
* [mikefarah/yq](https://github.com/mikefarah/yq) - yq 是一个便携式命令行 YAML、JSON、XML、CSV 和属性处理器
* [mmstick/concurr](https://github.com/mmstick/concurr) - 具有客户端-服务器架构的替代 GNU Parallel
* [mmstick/parallel](https://github.com/mmstick/parallel) - 重新实现 GNU Parallel
* [mprocs](https://github.com/pvolok/mprocs) - 在一个终端中运行多个命令
* [nix-community/nix-init](https://github.com/nix-community/nix-init) - 从 URL 生成 Nix 包
* [nomino](https://github.com/yaa110/nomino) - 开发人员的批量重命名实用程序
* [raftario/licensor](https://github.com/raftario/licensor) - 将许可证写入 stdout
* [replydev/cotp](https://github.com/replydev/cotp) - 值得信赖、加密的命令行 TOTP/HOTP 身份验证器应用程序，具有导入功能
* [rustdesk/rustdesk](https://github.com/rustdesk/rustdesk) - 远程桌面软件，开箱即用，无需配置
* [rustscan/rustscan](https://github.com/RustScan/RustScan) - 让 Nmap 更快
* [sorairolake/qrtool](https://github.com/sorairolake/qrtool) - 用于编码和解码 QR 码图像的实用程序
* [str4d/rage](https://github.com/str4d/rage) - age 的 Rust 实现
* [tversteeg/emplace](https://github.com/tversteeg/emplace) - 在多台机器上同步已安装的包
* [vamolessa/verco](https://github.com/vamolessa/verco) - 简单的 Git/Hg tui 客户端，专注于键盘快捷键
* [vaultwarden](https://github.com/dani-garcia/vaultwarden/) - Bitwarden 服务器 API 的替代实现
* [warpdotdev/Warp](https://github.com/warpdotdev/Warp) - Warp 是一个极速、基于 Rust 的 GPU 加速现代终端，内置 AI
* [whitfin/s3-concat](https://github.com/whitfin/s3-concat) - 使用灵活模式远程连接 Amazon S3 文件的命令行工具
* [whitfin/s3-meta](https://github.com/whitfin/s3-meta) - 用于收集有关 Amazon S3 存储桶的元数据的命令行工具
* [whitfin/s3-utils](https://github.com/whitfin/s3-utils) - 包含基于 Amazon S3 的额外便利实用程序的小工具
* [yaa110/cb](https://github.com/yaa110/cb) - 管理剪贴板的命令行界面

### 视频

* [ffmpeg-sidecar](https://github.com/nathanbabcock/ffmpeg-sidecar) - 在直观的迭代器接口中包装独立的 FFmpeg 二进制文件

### 虚拟化

* [beneills/quantum](https://github.com/beneills/quantum) - 高级量子计算机模拟器
* [bytecodealliance/wasmtime](https://github.com/bytecodealliance/wasmtime) - WebAssembly 的独立运行时
* [chromium/chromiumos/platform/crosvm](https://chromium.googlesource.com/chromiumos/platform/crosvm/) - CrOSVM 使 Chrome OS 能够在快速、安全的虚拟化环境中运行 Linux 应用程序
* [oxidecomputer/propolis](https://github.com/oxidecomputer/propolis) - illumos bhyve 内核模块的用户空间程序
* [saurvs/hypervisor-rs](https://github.com/saurvs/hypervisor-rs) - OS X 上的硬件加速虚拟化

### Web

* [LemmyNet/lemmy](https://github.com/LemmyNet/lemmy) - 联邦宇宙的链接聚合器/Reddit 克隆
* [libreddit](https://github.com/libreddit/libreddit) - Reddit 的私有前端
* [MASQ-Project/Node](https://github.com/MASQ-Project/Node) - MASQ 节点软件为全球用户提供去中心化网状网络
* [Plume-org/Plume](https://github.com/Plume-org/Plume) - 联合博客引擎

### Web 服务器

* [mufeedvh/binserve](https://github.com/mufeedvh/binserve) - 极速静态 Web 服务器，在单个二进制文件中具有路由、模板和安全性，您可以使用零代码设置
* [orhun/rustypaste](https://github.com/orhun/rustypaste) - 最小文件上传/pastebin 服务
* [static-web-server](https://github.com/static-web-server/static-web-server) - 用于静态文件服务的极速异步 Web 服务器
* [svenstaro/miniserve](https://github.com/svenstaro/miniserve) - 小型、自包含的跨平台 CLI 工具，允许您只获取二进制文件并通过 HTTP 提供一些文件
* [TheWaWaR/simple-http-server](https://github.com/TheWaWaR/simple-http-server) - 简单的静态 HTTP 服务器
* [thecoshman/http](https://github.com/thecoshman/http) - 请托管这些东西 — 一个基本的 HTTP 服务器，用于快速简单地托管文件夹
* [wyhaya/see](https://github.com/wyhaya/see) - 静态 HTTP 文件服务器

## 开发工具

* [bacon](https://github.com/Canop/bacon) - 后台 Rust 代码检查器
* [cargo-benchcmp](https://crates.io/crates/cargo-benchcmp) - 比较 Rust 微基准测试的实用程序
* [cargo-bitbake](https://crates.io/crates/cargo-bitbake) - 可以利用 meta-rust 中的类为 Yocto 生成 BitBake 配方的 cargo 扩展
* [cargo-cache](https://crates.io/crates/cargo-cache) - 检查/管理/清理您的 cargo 缓存
* [cargo-check](https://crates.io/crates/cargo-check) - 围绕 `cargo rustc -- -Zno-trans` 的包装器，如果您只需要正确性检查，它可以帮助运行更快的编译
* [cargo-count](https://crates.io/crates/cargo-count) - 列出源代码计数和 Rust 项目的详细信息，包括不安全统计信息
* [cargo-deb](https://crates.io/crates/cargo-deb) - 生成二进制 Debian 包
* [cargo-deps](https://crates.io/crates/cargo-deps) - 构建 Rust 项目的依赖图
* [cargo-do](https://crates.io/crates/cargo-do) - 连续运行多个 cargo 命令
* [cargo-ebuild](https://crates.io/crates/cargo-ebuild) - 可以使用树内 eclasses 为 Gentoo 生成 ebuild 的 cargo 扩展
* [cargo-edit](https://crates.io/crates/cargo-edit) - 允许您通过从命令行读取/写入 Cargo.toml 文件来添加和列出依赖项
* [cargo-generate](https://github.com/cargo-generate/cargo-generate) - 通过利用预先存在的 git 存储库作为模板来生成 rust 项目
* [cargo-info](https://crates.io/crates/cargo-info) - 从命令行查询 crates.io 以获取 crate 详细信息
* [cargo-license](https://crates.io/crates/cargo-license) - 快速显示所有依赖项的许可证
* [cargo-make](https://crates.io/crates/cargo-make) - Rust 任务运行器和构建工具
* [cargo-modules](https://crates.io/crates/cargo-modules) - 显示 crate 模块树概述的 cargo 插件
* [cargo-multi](https://crates.io/crates/cargo-multi) - 在多个 crate 上运行指定的 cargo 命令
* [cargo-outdated](https://crates.io/crates/cargo-outdated) - 显示 Rust 依赖项的最新版本何时过时
* [cargo-release](https://crates.io/crates/cargo-release) - 用于发布 git 管理的 cargo 项目、构建、标记、发布、文档和推送的工具
* [cargo-script](https://crates.io/crates/cargo-script) - 让人们快速轻松地运行 Rust "脚本"，可以利用 Cargo 的包生态系统
* [cargo-udeps](https://github.com/est31/cargo-udeps) - 查找未使用的依赖项
* [cargo-update](https://crates.io/crates/cargo-update) - 用于检查和应用更新到已安装的可执行文件的 cargo 子命令
* [cargo-watch](https://crates.io/crates/cargo-watch) - 源更改时的 cargo 实用程序
* [dtolnay/cargo-expand](https://github.com/dtolnay/cargo-expand) - 在源代码中展开宏

## 库

### 人工智能

#### 遗传算法

* [innoave/genevo](https://github.com/innoave/genevo) - 以可定制和可扩展的方式执行遗传算法（GA）模拟
* [m-decoster/RsGenetic](https://github.com/m-decoster/RsGenetic) - 遗传算法库。处于维护模式
* [Martin1887/oxigen](https://github.com/Martin1887/oxigen) - 快速、并行、可扩展和适应性强的遗传算法库
* [pkalivas/radiate](https://github.com/pkalivas/radiate) - 可定制的并行遗传编程引擎，能够进化解决方案以解决监督、无监督和强化学习问题
* [willi-kappler/darwin-rs](https://github.com/willi-kappler/darwin-rs) - 遗传算法库

#### 机器学习

* [candle](https://github.com/huggingface/candle) - 极简 ML 框架
* [coreylowman/dfdx](https://github.com/coreylowman/dfdx) - CUDA 加速机器学习框架，使用 Rust 中的张量和神经网络
* [huggingface/tokenizers](https://github.com/huggingface/tokenizers) - 今天最常用的分词器，具有研究和生产的重点
* [LaurentMazare/tch](https://github.com/LaurentMazare/tch) - PyTorch 的 Rust 绑定
* [linfa](https://github.com/rust-ml/linfa) - 机器学习框架
* [rust-ml/linfa](https://github.com/rust-ml/linfa) - 机器学习工具包
* [smartcorelib/smartcore](https://github.com/smartcorelib/smartcore) - 机器学习库

#### OpenAI

* [64bit/async-openai](https://github.com/64bit/async-openai) - 基于 OpenAI REST API 的人体工程学 Rust 绑定
* [zurawiki/gptcommit](https://github.com/zurawiki/gptcommit) - 使用 GPT-3 编写提交消息的 git prepare-commit-msg 钩子

#### 工具

* [floneum/floneum](https://github.com/floneum/floneum) - 用于控制大型语言模型的图形编辑器

### 天文学

* [cds-astro/aladin-lite](https://github.com/cds-astro/aladin-lite) - 用于可视化天文图像调查和叠加目录的 Web 应用程序
* [fitsio](https://crates.io/crates/fitsio) - fits 接口库包装 cfitsio
* [flosse/rust-sun](https://github.com/flosse/rust-sun) - JS 库 suncalc 的 Rust 端口
* [saurvs/astro-rust](https://github.com/saurvs/astro-rust) - 天文学

### 异步

* [async-std](https://async.rs/) - Rust 标准库的异步版本
* [dtolnay/async-trait](https://github.com/dtolnay/async-trait) - trait 中的异步函数类型擦除
* [futures-rs](https://github.com/rust-lang/futures-rs) - Rust 中的零成本 futures
* [mio](https://github.com/tokio-rs/mio) - 最小便携 API，构建在操作系统的事件通知系统之上
* [rust-lang/async-book](https://rust-lang.github.io/async-book/) - 异步编程
* [Stebalien/tempfile](https://github.com/Stebalien/tempfile) - 临时文件库
* [tokio-rs/tokio](https://github.com/tokio-rs/tokio) - 用于编写可靠、异步和精简应用程序的运行时

### 音频和音乐

* [hound](https://crates.io/crates/hound) - WAV 编码和解码库
* [insomnimus/nodi](https://github.com/insomnimus/nodi) - 用于播放和抽象 MIDI 文件的库
* [ozankasikci/rust-music-theory](https://github.com/ozankasikci/rust-music-theory) - Rust 音乐理论库
* [pdeljanov/Symphonia](https://github.com/pdeljanov/Symphonia) - 音频解码和媒体解复用库，支持 AAC、FLAC、MP3、MP4、OGG、Vorbis 和 WAV
* [RustAudio](https://github.com/RustAudio) - Rust 音频库的聚合
* [RustAudio/cpal](https://github.com/RustAudio/cpal) - 纯 Rust 中的低级跨平台音频 I/O 库
* [RustAudio/rodio](https://github.com/RustAudio/rodio) - Rust 音频播放库
* [RustAudio/rust-portaudio](https://github.com/RustAudio/rust-portaudio) - PortAudio 绑定

### 身份验证

* [constantoine/totp-lite](https://github.com/constantoine/totp-lite) - 简单的 TOTP（基于时间的一次性密码）库
* [Keats/jsonwebtoken](https://github.com/Keats/jsonwebtoken) - JSON Web Token 库
* [oauth2](https://github.com/ramosbugs/oauth2-rs) - 可扩展、强类型的 OAuth2 客户端库
* [oxide-auth](https://github.com/HeroicKatora/oxide-auth) - OAuth2 服务器库，用于与 actix 或其他前端结合使用，具有一组可配置和可插拔的后端
* [sgrust01/jwtvault](https://github.com/sgrust01/jwtvault) - 用于管理和编排 JWT 工作流的异步库
* [yup-oauth2](https://github.com/dermesser/yup-oauth2) - 提供设备、已安装和服务帐户流的 oauth2 客户端实现

### 汽车

* [marcelbuesing/can-dbc](https://github.com/marcelbuesing/can-dbc) - DBC 格式的解析器
* [marcelbuesing/tokio-socketcan](https://github.com/marcelbuesing/tokio-socketcan) - Linux SocketCAN 的 tokio 支持
* [mbr/socketcan](https://github.com/socketcan-rs/socketcan-rs) - Linux SocketCAN 库
* [oefd/tokio-socketcan](https://github.com/oefd/tokio-socketcan) - 基于 socketcan crate 的 Linux SocketCAN 支持

### 生物信息学

* [Rust-Bio](https://github.com/rust-bio) - Rust 中的生物信息学库

### 缓存

* [06chaynes/http-cache](https://github.com/06chaynes/http-cache) - 遵循 HTTP 缓存规则的缓存中间件
* [aisk/rust-memcache](https://github.com/aisk/rust-memcache) - Memcached 客户端库
* [al8n/stretto](https://github.com/al8n/stretto) - 高性能线程安全内存绑定 Rust 缓存
* [jaemk/cached](https://github.com/jaemk/cached) - 简单函数缓存/记忆化
* [mozilla/sccache](https://github.com/mozilla/sccache/) - 共享编译缓存，非常适合 Rust 编译

### 云

* AWS
  * [awslabs/aws-lambda-rust-runtime](https://github.com/awslabs/aws-lambda-rust-runtime) - AWS Lambda 的 Rust 运行时
  * [awslabs/aws-sdk-rust](https://github.com/awslabs/aws-sdk-rust) - 新的 AWS SDK for Rust
* 负载均衡器
  * [Convey](https://github.com/bparli/convey) - 具有动态配置加载的第 4 层负载均衡器

### 命令行

* 参数解析
  * [clap-rs](https://github.com/clap-rs/clap) - 简单易用、功能齐全的命令行参数解析器
  * [cliparser](https://crates.io/crates/cliparser) - 简单的命令行解析器
  * [docopt/docopt.rs](https://github.com/docopt/docopt.rs) - docopt 的 Rust 实现
  * [google/argh](https://github.com/google/argh) - 基于代码生成的优化参数解析库
  * [killercup/quicli](https://github.com/killercup/quicli) - 在 Rust 中快速构建酷炫的 CLI 应用程序
  * [ksk001100/seahorse](https://github.com/ksk001100/seahorse) - 用 Rust 编写的最小 CLI 框架
  * [TeXitoi/structopt](https://github.com/TeXitoi/structopt) - 通过定义结构来解析命令行参数
* 数据可视化
  * [nukesor/comfy-table](https://github.com/nukesor/comfy-table) - 为您的 CLI 工具提供美观的动态表格
  * [reugn/rspark](https://github.com/reugn/rspark) - 用于 Rust 的迷你图库
  * [zhiburt/tabled](https://github.com/zhiburt/tabled) - 易于使用的库，用于 Rust 结构和枚举的漂亮打印表格
* 人机界面设备
  * [gilrs](https://gitlab.com/gilrs-project/gilrs) - 纯 Rust 游戏输入库
* 行编辑器
  * [kkawakam/rustyline](https://github.com/kkawakam/rustyline) - Rust 中的 readline 实现
  * [MovingtoMars/liner](https://github.com/MovingtoMars/liner) - 提供类似 readline 功能的库
  * [murarth/linefeed](https://github.com/murarth/linefeed) - 可配置、可扩展、交互式行阅读器
  * [srijs/rust-copperline](https://github.com/srijs/rust-copperline) - 纯 Rust 命令行编辑库
* 管道
  * [hniksic/rust-subprocess](https://github.com/hniksic/rust-subprocess) - 与外部命令交互的设施
  * [imp/pager-rs](https://gitlab.com/imp/pager-rs) - 通过外部分页器管道输出
  * [oconnor663/duct.rs](https://github.com/oconnor663/duct.rs) - 子进程管道和 IO 重定向的构建器
  * [philippkeller/rexpect](https://github.com/philippkeller/rexpect) - 自动化交互式应用程序，如 ssh、ftp、passwd 等
  * [rust-cli/rexpect](https://github.com/rust-cli/rexpect) - 期望库，用于自动化交互式应用程序
* 进度
  * [a8m/pb](https://github.com/a8m/pb) - Rust 的控制台进度条
  * [console-rs/indicatif](https://github.com/console-rs/indicatif) - 向用户指示进度
  * [etienne-napoleone/spinach](https://github.com/etienne-napoleone/spinach) - 实用的微调器
  * [FGRibreau/spinners](https://github.com/FGRibreau/spinners) - 60 多个优雅的终端微调器
* 提示
  * [hashmismatch/terminal_cli.rs](https://github.com/hashmismatch/terminal_cli.rs) - 构建交互式命令提示符
  * [mikaelmello/inquire](https://github.com/mikaelmello/inquire) - 用于在终端上构建交互式提示的库
  * [starship/starship](https://starship.rs/) - 适用于任何 shell 的最小、极快且高度可定制的提示符
  * [ynqa/promkit](https://github.com/ynqa/promkit) - 用于构建交互式命令行工具的工具包
* 样式
  * [colored](https://github.com/colored-rs/colored) - 着色终端，如此简单，您已经知道如何做了！
  * [console-rs/dialoguer](https://github.com/console-rs/dialoguer) - Rust 命令行提示库
  * [LukasKalbertodt/bunt](https://github.com/LukasKalbertodt/bunt) - 跨平台终端颜色和样式，具有类似宏的语法
  * [LukasKalbertodt/term-painter](https://github.com/LukasKalbertodt/term-painter) - 跨平台样式终端输出
  * [ogham/rust-ansi-term](https://github.com/ogham/rust-ansi-term) - 控制 ANSI 终端上的颜色和格式
  * [SergioBenitez/yansi](https://github.com/SergioBenitez/yansi) - 死简单的 ANSI 终端颜色绘制库
* TUI
  * BearLibTerminal
    * [cfyzium/bearlibterminal](https://github.com/nabijaczleweli/BearLibTerminal.rs) - BearLibTerminal 绑定
  * [crossterm-rs/crossterm](https://github.com/crossterm-rs/crossterm) - 跨平台终端库
  * [fdehau/tui-rs](https://github.com/fdehau/tui-rs) - 受 blessed-contrib 和 termui 启发的 TUI 库
  * [gyscos/Cursive](https://github.com/gyscos/cursive) - 构建丰富的 TUI 应用程序
  * [ivanceras/titik](https://github.com/ivanceras/titik) - 跨平台 TUI 小部件库，目标是提供交互式小部件
  * ncurses
    * [ihalila/pancurses](https://github.com/ihalila/pancurses) - curses 库，支持 linux 和 windows
    * [jeaye/ncurses-rs](https://github.com/jeaye/ncurses-rs) - ncurses 绑定
  * [ratatui-org/ratatui](https://github.com/ratatui-org/ratatui) - 用于构建丰富终端用户界面和仪表板的库
  * [redox-os/termion](https://github.com/redox-os/termion) - 用于控制终端/TTY 的无绑定库
  * Termbox
    * [gchp/rustbox](https://github.com/gchp/rustbox) - termbox 的 Rust 实现

### 压缩

* [7-zip](https://7-zip.org/)
  * [dyz1990/sevenz-rust](https://github.com/dyz1990/sevenz-rust) - 7z 解压缩器/压缩器，支持 LZMA/LZMA2/BZIP2/DEFLATE/DELTA/BCJ/BCJ2/COPY/CRYPTO_AES256_SHA256
* [Brotli](https://opensource.googleblog.com/2015/09/introducing-brotli-new-compression.html)
  * [dropbox/rust-brotli](https://github.com/dropbox/rust-brotli) - Brotli 解压缩器，可选择避免标准库
  * [ende76/brotli-rs](https://github.com/ende76/brotli-rs) - Brotli 压缩的实现
* bzip2
  * [alexcrichton/bzip2-rs](https://github.com/alexcrichton/bzip2-rs) - libbz2 绑定
* gzip
  * [zopfli](https://github.com/zopfli-rs/zopfli) - Zopfli 压缩算法的实现，用于更高质量的 deflate 或 zlib 压缩
* gzp
  * [sstadick/gzp](https://github.com/sstadick/gzp) - 多线程编码和解码 deflate 格式和 snappy
* miniz
  * [rust-lang/flate2-rs](https://github.com/rust-lang/flate2-rs) - miniz 绑定
* snappy
  * [JeffBelgum/rust-snappy](https://github.com/JeffBelgum/rust-snappy) - snappy 绑定
* tar
  * [alexcrichton/tar-rs](https://github.com/alexcrichton/tar-rs) - Rust 中的 tar 存档读取/写入
* zip
  * [zip-rs/zip](https://github.com/zip-rs/zip) - 读取和写入 ZIP 存档

### 计算

* [argmin-rs/argmin](https://github.com/argmin-rs/argmin) - 纯 Rust 优化库
* [BLAS](https://en.wikipedia.org/wiki/Basic_Linear_Algebra_Subprograms)
  * [mikkyang/rust-blas](https://github.com/mikkyang/rust-blas) - BLAS 绑定
* [calebwin/emu](https://github.com/calebwin/emu) - 用于 GPGPU 数值计算的语言
* [dimforge/nalgebra](https://github.com/dimforge/nalgebra) - 低维线性代数库
* [faer-rs](https://github.com/sarah-ek/faer-rs) - 线性代数基础
* [GSL](http://www.gnu.org/software/gsl/)
  * [GuillaumeGomez/rust-GSL](https://github.com/GuillaumeGomez/rust-GSL) - GSL 绑定
* [LAPACK](https://en.wikipedia.org/wiki/LAPACK)
  * [stainless-steel/lapack](https://github.com/blas-lapack-rs/lapack) - LAPACK 绑定
* 并行
  * [arrayfire/arrayfire-rust](https://github.com/arrayfire/arrayfire-rust) - Arrayfire 绑定
  * [autumnai/collenchyma](https://github.com/autumnai/collenchyma) - 可扩展、可插拔、后端无关的框架，用于在 CUDA、OpenCL 和通用主机 CPU 上进行并行、高性能计算
  * [luqmana/rust-opencl](https://github.com/luqmana/rust-opencl) - OpenCL 绑定
* 科学计算
  * [cpmech/russell](https://github.com/cpmech/russell) - Rust 科学库，用于数值数学、常微分方程、特殊函数、高性能（稀疏）线性代数
  * [indigits/scirust](https://github.com/indigits/scirust) - Rust 科学计算库
* 统计学
  * [boxtown/statrs](https://github.com/boxtown/statrs) - 强大的统计计算库
  * [statrs-dev/statrs](https://github.com/statrs-dev/statrs) - 强大的统计计算库

## 注册表

注册表允许您将 Rust 库作为 crate 包发布，以便与他人公开或私下共享。

* [cenotelie/cratery](https://github.com/cenotelie/cratery) - 轻量级私有 cargo 注册表，内置电池，为组织构建，包括类似 docs.rs 和 deps.rs 的功能
* [Cloudsmith 💲](https://cloudsmith.com/product/formats/cargo-registry) - 完全托管的包管理 SaaS，对公共和私有 Cargo/Rust 注册表提供一流支持（以及许多其他）。有慷慨的免费层，对开源也完全免费
* [Crates](https://crates.io) - Rust/Cargo 的官方公共注册表
* [RepoFlow](https://www.repoflow.io) - 简单现代的存储库平台，可以托管 Rust crate 存储库并代理 crates.io。还支持其他包类型，如 Docker、PyPI、Maven、npm 和 RubyGems。可作为云服务或自托管使用
* [w4/chartered](https://github.com/w4/chartered) - 私有、经过身份验证、有权限的 Cargo 注册表

## 资源

* [Rust 简史。第 1 部分](https://medium.com/rustaceans/make-it-mandatory-a-brief-history-of-rust-part-1-805459c60c6b) - 从开发者对软件稳定性的追求到几乎破坏其创造者稳定性的项目。[第 2 部分](https://medium.com/rustaceans/make-it-mandatory-a-brief-history-of-rust-part-2-981d61451aa5)。[第 3 部分](https://medium.com/rustaceans/make-it-mandatory-a-brief-history-of-rust-part-2-b8c0f7a7e781?sk=c0e7fe5fde11a62edc23f284f125aa18)
* 基准测试
  * [c410-f3r/wtx-bench](https://github.com/c410-f3r/wtx-bench) - Web 基准测试
  * [TeXitoi/benchmarksgame-rs](https://github.com/TeXitoi/benchmarksgame-rs) - 计算机语言基准游戏的实现
* 演示文稿和幻灯片
  * [使用 Rust 学习系统编程](https://speakerdeck.com/jvns/learning-systems-programming-with-rust) - Julia Evans 在 Rustconf 2016 上的演讲
  * [Rust: 无畏编程！](https://www.youtube.com/watch?v=lO1z-7cuRYI) - Nicholas Matsakis 在 C++Now 2018 上的演讲
  * [发布稳定的 Rust Crate](https://www.youtube.com/watch?v=t4CyEKb-ywA) - Michael Gattozzi 在 RustConf 2017 上的演讲
* 学习资源
  * [100 个练习学习 Rust](https://rust-exercises.com) - 通过 100 个实践练习学习 Rust，涵盖语法、类型等
  * [Aquascope](https://github.com/cognitive-engineering-lab/aquascope) - Rust 编译时和运行时的交互式可视化
  * [Awesome Rust Streaming](https://github.com/jamesmunns/awesome-rust-streaming) - 社区策划的直播列表
  * [awesome-rust-mentors](https://rustbeginners.github.io/awesome-rust-mentors/) - 愿意接受学员并教育他们 Rust 和编程的有用导师列表
  * [CIS 198: Rust 编程](http://cis198-2016s.github.io/schedule/) - 宾夕法尼亚大学计算机科学 Rust 编程课程
  * [CodeCrafters.io](https://app.codecrafters.io/tracks/rust) - 构建您自己的 Redis、Git、Docker 或 SQLite
  * [Comprehensive Rust 🦀](https://google.github.io/comprehensive-rust/) - 3 天 Rust 基础课程加上 Android、裸机 Rust 和并发的 1 天课程。提供英语、巴西葡萄牙语和韩语版本
  * [Easy Rust](https://github.com/Dhghomon/easy_rust) - 用简单英语学习 Rust
  * [exercism.org](https://exercism.org/tracks/rust) - 帮助您学习 Rust 新概念的编程练习
  * [Hands-on Rust](https://pragprog.com/titles/hwrust/hands-on-rust/) - 通过制作游戏学习 Rust 的实践指南 - Herbert Wolverson 著（付费）
  * [Idiomatic Rust](https://github.com/mre/idiomatic-rust) - 教授惯用 Rust 的同行评议文章/演讲/存储库集合
  * [LabEx Rust 技能树](https://labex.io/skilltrees/rust) - 结构化的 Rust 学习路径，包含实践实验室，专为初学者逐步掌握 Rust 而设计
  * [Learn Rust 101](https://rust-lang.guide/) - 帮助您成为 Rustacean（Rust 开发者）的指南
  * [用 500 行代码学习 Rust](https://github.com/cuppar/rtd) - 用 500 行代码学习 Rust，从头开始构建 Todo CLI 应用程序
  * [用太多链表学习 Rust](https://rust-unofficial.github.io/too-many-lists/) - 通过实现几种不同类型的列表结构深入探索 Rust 的内存管理规则
  * [Rust 小书集](https://lborb.github.io/book/) - 精选的 Rust 书籍和操作指南列表
  * [编程社区精选的 Rust 学习资源](https://hackr.io/tutorials/learn-rust) - 编程社区投票推荐的资源列表
  * [重构到 Rust](https://www.manning.com/books/refactoring-to-rust) - 介绍 Rust 语言的书籍
  * [Rust by Example](https://doc.rust-lang.org/rust-by-example/) - 说明各种 Rust 概念和标准库的可运行示例集合
  * [Rust Cookbook](https://rust-lang-nursery.github.io/rust-cookbook/) - 简单示例集合，演示使用 Rust 生态系统的 crate 完成常见编程任务的良好实践
  * [Rust 闪卡](https://github.com/ad-si/Rust-Flashcards) - 超过 550 张闪卡，从第一原理学习 Rust
  * [专业人士的 Rust](https://overexact.com/rust-for-professionals/) - 为有经验的软件开发者快速介绍 Rust
  * [Rust Gym](https://github.com/warycat/rustgym) - 用 Rust 解决的大量编程面试问题集合
  * [Rust in Action](https://www.manning.com/books/rust-in-action) - Tim McNamara 的 Rust 系统编程实践指南（付费）
  * [Rust in Motion](https://www.manning.com/livevideo/rust-in-motion?a_aid=cnichols&a_bid=6a993c2e) - Carol Nichols 和 Jake Goulding 的视频系列（付费）
  * [Rust 语言备忘单](https://cheats.rs/) - Rust 语言备忘单
  * [Rust Tiếng Việt](https://rust-tieng-viet.github.io/) - 用越南语学习 Rust
  * [rust-how-do-i-start](https://github.com/jondot/rust-how-do-i-start) - 专门回答"那么，Rust。我如何开始？"问题的存储库。仅限初学者的精选资源和学习轨道
  * [rust-learning](https://github.com/ctjhoa/rust-learning) - 学习 Rust 的有用资源集合
  * [Rustfinity](https://www.rustfinity.com) - 通过实践练习和挑战练习 Rust 的交互式平台
  * [Rustlings](https://github.com/rust-lang/rustlings) - 让您习惯阅读和编写 Rust 代码的小练习
  * [Rusty CS](https://github.com/AbdesamedBendjeddou/Rusty-CS) - 帮助在 Rust 中实践所获得的学术知识的计算机科学课程
  * [stdx](https://github.com/brson/stdx) - 首先学习这些 crate 作为 std 的扩展
  * [Rust 之旅](https://tourofrust.com) - 这是一个交互式的逐步指南，介绍 Rust 编程语言的功能
* 播客
  * [New Rustacean](https://newrustacean.com) - 关于学习 Rust 的播客
  * [Rustacean Station](https://rustacean-station.org/) - 为 Rust 创建播客内容的社区项目
* [Rust 设计模式](https://github.com/rust-unofficial/patterns) - Rust 设计模式、反模式和习语目录
* [Rust 指南](http://aturon.github.io/) - Aaron Turon 关于 Rust 的博客文章
* [Rust 服务器、服务和应用程序 - MEAP](https://www.manning.com/books/rust-servers-services-and-apps) - 在 Rust 中构建后端服务器、服务和前端，以获得快速、可靠和可维护的应用程序
* [Rust Subreddit](https://www.reddit.com/r/rust/) - 发布和讨论 Rust 相关问题、文章和资源的子版块（论坛）
* [RustBooks](https://github.com/sger/RustBooks) - RustBooks 列表
* [RustCamp 2015 演讲](https://www.youtube.com/playlist?list=PLE7tQUdRKcybdIw61JpCoo89i4pWU5f_t) - RustCamp 2015 录制的演讲
* [RustViz](https://github.com/rustviz/rustviz) - 从简单的 Rust 程序生成可视化，帮助用户更好地理解 Rust 生命周期和借用机制
* [观看 Jon Gjengset 在 Rust 中实现 BitTorrent](https://www.youtube.com/watch?v=jf_ddGnum_4) - 在 Rust 中实现（部分）BitTorrent 客户端

## 许可证

[![CC0](https://licensebuttons.net/p/zero/1.0/88x31.png)](https://creativecommons.org/publicdomain/zero/1.0/)

---

**注意：** 这是 [rust-unofficial/awesome-rust](https://github.com/rust-unofficial/awesome-rust) 项目的中文翻译版本。原项目内容非常庞大，本文档包含了主要的分类和重要项目。如需查看完整内容，请访问原始项目。

**贡献：** 如果您想为这个中文版本做出贡献或发现任何错误，欢迎提交 issue 或 pull request。

**更新：** 本文档基于原项目的某个时间点创建，可能不包含最新的更新。建议定期查看原项目以获取最新信息。
