<execution>
<constraint>
  ## 矛盾分析技术约束
  - **GitHub Issues专用**：必须在product子模块中创建和管理矛盾分析Issues
  - **标准模板强制**：严格使用标准矛盾分析模板，包含所有必需要素
  - **状态追踪要求**：每个状态切换必须记录关键决策点和转化特征
  - **三轨制架构**：严格区分矛盾轨道、需求轨道、任务轨道的职责边界
</constraint>

<rule>
  ## 矛盾分析强制执行规则
  - **角色4特征完整性**：使用目的、痛点需求、能力水平、决策权限必须全部分析
  - **对立面深度分析**：必须识别内在推动力量和阻力，判断主导方面
  - **状态流程严格性**：严格按照六阶段状态推进：待分析→分析中→方案制定→实施中→已解决→已转化
  - **载体转化追踪**：必须识别和记录矛盾解决过程中的载体转化特征
</rule>

<guideline>
  ## 矛盾分析指导原则
  - **标准化流程优先**：使用成熟的矛盾识别→创建→分析→推进流程
  - **可视化表达**：复杂流程使用Mermaid图形化表达，提高理解效率
  - **关系网络建立**：建立矛盾之间的关系链接，识别系统性问题
  - **质量标准坚持**：确保每个分析都达到深入准确的质量要求
</guideline>

<process>
  ## 矛盾分析执行流程

  ### Step 1: 矛盾识别与创建
  ```mermaid
  flowchart TD
      A[识别潜在矛盾] --> B[角色4特征分析]
      B --> C[判断矛盾类型]
      C --> D[创建product子模块Issue]
      D --> E[应用标准模板]
  ```

  ### Step 2: 基本信息设定
  - **状态设定**：🔍 待分析
  - **强度评估**：🔥激烈 ⚡突出 📊一般 🌊缓和  
  - **来源标记**：🔮预测 🔍实践 🔄转化

  ### Step 3: 角色与场景定位
  - **使用目的**：为什么要用PromptX
  - **痛点需求**：遇到什么问题需要解决
  - **能力水平**：技术能力和使用经验
  - **决策权限**：能够决定什么

  ### Step 4: 对立面分析
  - **🔸对立面A**：内在推动力量及表现形式
  - **🔹对立面B**：内在阻力及表现形式
  - **主导方面判断**：当前哪种力量占主导，为什么

  ### Step 5: 状态推进管理
  ```bash
  🔍待分析 → 📝分析中 → 💡方案制定 → 🛠️实施中 → ✅已解决 → 🔄已转化
  ```

  **每个状态切换执行**：
  1. 更新Issue状态标签
  2. 记录关键决策点
  3. 识别载体转化特征
  4. 建立矛盾关系链接

  ### Step 6: 三轨制协调
  ```
  矛盾轨道 (product子模块Issues)
  ↓ 转化为
  需求轨道 (功能需求定义)
  ↓ 分解为  
  任务轨道 (具体开发任务)
  ```
</process>

<criteria>
  ## 矛盾分析质量标准

  ### 分析完整性
  - ✅ 使用标准矛盾分析模板
  - ✅ 角色4特征完整定义
  - ✅ 对立面分析深入准确
  - ✅ 载体转化路径清晰

  ### 流程规范性
  - ✅ 状态推进严格按流程执行
  - ✅ 每个状态切换有完整记录
  - ✅ 三轨制职责边界明确
  - ✅ 项目架构边界清晰

  ### 输出价值性
  - ✅ 分析结果具有实际指导意义
  - ✅ 载体转化特征识别准确
  - ✅ 矛盾关系网络建立有效
  - ✅ 为后续需求转化提供基础
</criteria>
</execution>