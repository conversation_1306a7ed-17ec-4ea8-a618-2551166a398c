name: Auto Version on Merge

on:
  pull_request:
    types: [closed]
    branches: [test, staging, main]

permissions:
  contents: write
  pull-requests: write

jobs:
  version-bump:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_PAT || github.token }}

      - name: Setup Node environment
        uses: ./.github/actions/setup-node

      - name: Configure Git
        uses: ./.github/actions/git-config

      - name: Check for release marker
        id: check-release
        run: |
          if [ -f ".release" ]; then
            RELEASE_TYPE=$(cat .release)
            echo "should-release=true" >> $GITHUB_OUTPUT
            echo "release-type=$RELEASE_TYPE" >> $GITHUB_OUTPUT
            rm .release
          else
            echo "should-release=false" >> $GITHUB_OUTPUT
          fi

      - name: Determine version bump type
        if: steps.check-release.outputs.should-release == 'true'
        id: version-type
        run: |
          case "${{ github.base_ref }}" in
            "test")
              BUMP_TYPE="patch"
              ;;
            "staging")
              BUMP_TYPE="minor"
              ;;
            "main")
              BUMP_TYPE="${{ steps.check-release.outputs.release-type }}"
              ;;
            *)
              BUMP_TYPE="patch"
              ;;
          esac
          echo "bump-type=$BUMP_TYPE" >> $GITHUB_OUTPUT

      - name: Bump version and generate changelog
        if: steps.check-release.outputs.should-release == 'true'
        uses: ./.github/actions/version-management
        with:
          bump-type: ${{ steps.version-type.outputs.bump-type }}
          commit-message: "chore: release"

      - name: Push changes
        if: steps.check-release.outputs.should-release == 'true'
        run: |
          git push origin ${{ github.base_ref }}

      - name: Create version tag
        if: steps.check-release.outputs.should-release == 'true' && github.base_ref == 'main'
        run: |
          VERSION=$(node -p "require('./package.json').version")
          git tag -a "v$VERSION" -m "Release v$VERSION"
          git push origin "v$VERSION"

      - name: Create sync PR
        if: steps.check-release.outputs.should-release == 'true' && github.base_ref == 'test'
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GH_PAT || github.token }}
          branch: chore/sync-test-to-develop
          base: develop
          title: "chore: sync version from test to develop"
          body: |
            ## 🔄 版本同步
            
            自动将 test 分支的版本变更同步回 develop 分支。
            
            ### 变更内容
            - 版本号更新
            - Changelog 更新
            
            ---
            *此 PR 由 GitHub Actions 自动创建*
          labels: |
            auto-merge
            version-sync
          delete-branch: true