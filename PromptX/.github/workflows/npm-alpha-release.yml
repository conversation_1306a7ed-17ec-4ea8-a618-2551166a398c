name: NPM Alpha Release

on:
  push:
    branches:
      - test
    paths-ignore:
      # GitHub相关配置
      - '.github/**'
      # 文档目录
      - 'docs/**'
      # 根目录的README文件（但不包括prompt/和resource/下的.md文件）
      - 'README*.md'
      - 'CHANGELOG.md'
      - 'TEST_ALPHA.md'
      # 配置文件
      - 'LICENSE'
      - '.gitignore'
      - '.npmignore'
      - '.cursorrules'
      - 'commitlint.config.js'
      # 资源文件
      - 'assets/**'
      # 测试覆盖率报告
      - 'coverage/**'

permissions:
  contents: write
  pull-requests: write

jobs:
  release-alpha:
    name: Release Alpha Version
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_PAT || github.token }}
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org/'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Get version info
        run: |
          PACKAGE_VERSION=$(node -p "require('./package.json').version")
          echo "PACKAGE_VERSION=$PACKAGE_VERSION" >> $GITHUB_ENV
          echo "Publishing version: $PACKAGE_VERSION"
      
      - name: Run tests
        run: pnpm run test:ci
      
      - name: Publish alpha version
        run: |
          # 检查是否已存在标准格式的alpha版本（只匹配.数字结尾的）
          EXISTING_ALPHA=$(npm view dpml-prompt versions --json | jq -r '.[]' | grep "^${{ env.PACKAGE_VERSION }}-alpha\.[0-9]\+$" | sort -V | tail -1 || echo "")
          
          if [ -z "$EXISTING_ALPHA" ]; then
            # 第一个alpha版本
            ALPHA_VERSION="${{ env.PACKAGE_VERSION }}-alpha.0"
          else
            # 提取现有版本号并递增
            CURRENT_NUM=$(echo "$EXISTING_ALPHA" | sed -E 's/.*-alpha\.([0-9]+)$/\1/')
            NEXT_NUM=$((CURRENT_NUM + 1))
            ALPHA_VERSION="${{ env.PACKAGE_VERSION }}-alpha.$NEXT_NUM"
          fi
          
          echo "Publishing alpha version: $ALPHA_VERSION"
          npm version "$ALPHA_VERSION" --no-git-tag-version
          npm publish --tag alpha --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}
      
      - name: Create Git tag
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag -a "v${{ env.PACKAGE_VERSION }}-alpha" -m "Release v${{ env.PACKAGE_VERSION }}-alpha"
          git push origin "v${{ env.PACKAGE_VERSION }}-alpha"
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT || github.token }}
      
      - name: Comment on PR
        uses: actions/github-script@v7
        with:
          script: |
            const version = '${{ env.PACKAGE_VERSION }}';
            const comment = `🎉 **Alpha version published!**
            
            📦 Version: \`${version}-alpha.0\`
            🏷️ Tag: \`alpha\`
            
            Install with:
            \`\`\`bash
            npm install dpml-prompt@alpha
            # or
            npm install dpml-prompt@${version}-alpha.0
            \`\`\``;
            
            // Find recent PRs
            const { data: prs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'closed',
              base: 'test',
              sort: 'updated',
              direction: 'desc',
              per_page: 5
            });
            
            // Comment on the most recent merged PR
            const recentPR = prs.find(pr => pr.merged_at);
            if (recentPR) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: recentPR.number,
                body: comment
              });
            }