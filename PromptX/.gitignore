# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Package manager files (keep pnpm-lock.yaml for reproducible builds)
package-lock.json
yarn.lock

# Build outputs
dist/
build/
/lib/
*.tsbuildinfo

# Testing
coverage/
.nyc_output
*.lcov

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Cache directories
.npm
.eslintcache
.node_repl_history

# Output of package managers
*.tgz
.yarn-integrity

# Temporary folders
tmp/
temp/

# Application specific
/.memory/
/.promptx/
/.idea/
/.vscode/

# Development
*.orig
.husky/_

.kilocode/
