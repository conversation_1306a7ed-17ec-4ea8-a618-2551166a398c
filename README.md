# MindForge 🧠⚒️

> **智能知识网络构建平台** - 从一个想法开始，通过AI辅助构建和管理个人知识网络

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Tauri](https://img.shields.io/badge/Tauri-1.5-blue.svg)](https://tauri.app/)
[![Rust](https://img.shields.io/badge/Rust-1.70+-orange.svg)](https://www.rust-lang.org/)
[![React](https://img.shields.io/badge/React-18-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)

## ✨ 项目愿景

**MindForge** 是一个革命性的知识管理工具，它将传统的笔记应用与现代AI技术相结合，帮助用户：

- 🎨 在**无限大画布**上自由构建知识网络
- 🤖 通过**AI智能辅助**扩展和关联想法
- 🕸️ 利用**图数据库**管理复杂的知识关系
- 🔍 享受**语义搜索**带来的智能内容发现
- ⚡ 体验**原生性能**的流畅桌面应用

## 🚀 核心特性

### 🎨 无限大画布
- 基于 Konva.js 的高性能 2D 渲染引擎
- 支持无限缩放和平移的自由创作空间
- 虚拟化渲染技术，轻松处理 1000+ 节点

### 🧠 AI 智能辅助
- 多AI模型适配（OpenAI、Claude、国产大模型）
- 智能内容生成和想法扩展
- 基于上下文的关联推荐
- 自动发现知识节点间的潜在关系

### 🕸️ 知识网络化
- SurrealDB 图数据库支持复杂关系查询
- 支持多种关系类型：关联、支持、矛盾、派生等
- 图分析算法：最短路径、社区发现、中心性分析
- 可视化知识网络结构和演化

### 🔍 智能搜索
- Qdrant 向量数据库驱动的语义搜索
- 混合搜索：关键词 + 语义 + 图结构
- 智能推荐相似内容和相关节点
- 基于用户行为的个性化搜索

### ⚡ 原生性能
- Tauri 架构：Rust 后端 + Web 前端
- 包体积仅 10-20MB（比 Electron 减少 80-90%）
- 内存占用减少 50-70%
- 启动时间 < 3 秒

## 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────┐
│                前端层 (React + TypeScript)               │
│  Canvas渲染 │ 状态管理 │ UI组件 │ 交互逻辑              │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    Tauri 应用框架                        │
│  IPC通信 │ 事件系统 │ 窗口管理 │ 系统集成               │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                  Rust 后端服务                          │
│  数据协调器 │ AI服务 │ 图服务 │ 向量服务               │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    数据存储层                            │
│  SQLite │ SurrealDB │ Qdrant │ 文件系统               │
│  配置数据 │ 图数据库 │ 向量库 │ 附件存储               │
└─────────────────────────────────────────────────────────┘
```

## 📦 快速开始

### 环境要求

- **Rust** 1.70+
- **Node.js** 18+
- **pnpm** 8+ (推荐) 或 npm/yarn

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-username/mindforge.git
cd mindforge

# 安装前端依赖
pnpm install

# 安装 Tauri CLI
cargo install tauri-cli
```

### 开发环境

```bash
# 启动开发服务器
pnpm tauri dev

# 或者分别启动前后端
pnpm dev          # 前端开发服务器
cargo tauri dev   # Tauri 开发模式
```

### 构建发布

```bash
# 构建生产版本
pnpm tauri build

# 构建结果在 src-tauri/target/release/bundle/ 目录
```

## 🛠️ 开发指南

### 项目结构

```
mindforge/
├── src/                    # 前端源码
│   ├── components/         # React 组件
│   ├── hooks/             # 自定义 Hooks
│   ├── stores/            # Zustand 状态管理
│   ├── utils/             # 工具函数
│   └── types/             # TypeScript 类型定义
├── src-tauri/             # Tauri 后端
│   ├── src/               # Rust 源码
│   │   ├── commands/      # Tauri 命令
│   │   ├── services/      # 业务服务
│   │   └── models/        # 数据模型
│   └── Cargo.toml         # Rust 依赖配置
├── public/                # 静态资源
└── docs/                  # 项目文档
```

### 核心技术栈

- **前端**: React 18 + TypeScript + Vite
- **UI库**: Ant Design / Mantine
- **画布**: Konva.js + react-konva
- **状态管理**: Zustand
- **后端**: Rust + Tauri
- **数据库**: SQLite + SurrealDB + Qdrant

### 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 代码规范
- 使用 Conventional Commits 提交规范
- 编写单元测试和集成测试

## 📊 性能对比

| 指标 | Electron 方案 | MindForge (Tauri) | 提升幅度 |
|------|--------------|-------------------|----------|
| 包体积 | 100-200MB | 10-20MB | **80-90%↓** |
| 内存占用 | 200-500MB | 100-200MB | **50-70%↓** |
| 启动时间 | 3-8秒 | 1-3秒 | **60-70%↓** |
| CPU占用 | 中等 | 低 | **30-50%↓** |

## 🗺️ 开发路线图

### 🎯 第一阶段：基础框架 (已完成)
- [x] 项目架构设计
- [x] 技术栈选型
- [x] 开发环境搭建

### 🚧 第二阶段：核心功能 (进行中)
- [ ] Tauri + React 基础架构
- [ ] 画布系统和节点管理
- [ ] 数据库集成 (SQLite + SurrealDB + Qdrant)
- [ ] 基础 AI 功能集成

### 📋 第三阶段：高级功能 (计划中)
- [ ] 智能搜索和推荐系统
- [ ] 图分析和可视化
- [ ] 导入导出功能
- [ ] 性能优化

### 🎉 第四阶段：完善发布 (计划中)
- [ ] 全面测试和优化
- [ ] 用户文档和帮助系统
- [ ] 打包和分发

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 如何贡献

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Tauri](https://tauri.app/) - 现代桌面应用框架
- [SurrealDB](https://surrealdb.com/) - 现代多模型数据库
- [Qdrant](https://qdrant.tech/) - 向量搜索引擎
- [Konva.js](https://konvajs.org/) - 2D Canvas 库

## 📞 联系我们

- 项目主页: [https://github.com/your-username/mindforge](https://github.com/your-username/mindforge)
- 问题反馈: [Issues](https://github.com/your-username/mindforge/issues)
- 讨论交流: [Discussions](https://github.com/your-username/mindforge/discussions)

---

**MindForge** - 让每个想法都有机会成长为知识的参天大树 🌳