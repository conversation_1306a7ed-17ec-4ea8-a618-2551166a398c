# 知识库管理系统 - 第一期技术方案

## 📋 项目概述

构建一个基于大画布的知识库管理系统，支持从一个想法开始，通过AI辅助扩展和管理知识网络。第一期专注于核心的知识库管理功能。

## 🎯 第一期功能范围

### 核心功能
- ✅ 可视化知识图谱展示（大画布）
- ✅ 知识节点的创建、编辑、删除
- ✅ 节点间关系连接和管理
- ✅ 全文搜索和语义搜索
- ✅ 知识库数据持久化存储
- ✅ 基础的AI集成（想法扩展）

### 暂不实现
- ❌ 复杂的多模态支持
- ❌ 实时协作功能
- ❌ 高级AI推理
- ❌ 移动端适配

## 🏗️ 技术架构选择

### 整体架构：Tauri + React + Rust 后端

**选择理由：**
- 降低开发难度：前端使用熟悉的React生态
- 性能优秀：Rust后端处理数据密集型操作
- 跨平台：一套代码支持Windows/macOS/Linux
- 打包简单：单一可执行文件分发

## 📚 核心技术栈

### 前端技术栈
```json
{
  "框架": "React 18 + TypeScript",
  "样式": "TailwindCSS",
  "画布库": "Konva.js + react-konva",
  "图可视化": "Cytoscape.js",
  "状态管理": "Zustand",
  "构建工具": "Vite",
  "UI组件": "Headless UI + 自定义组件"
}
```

### 后端技术栈 (Rust)
```toml
[dependencies]
# 桌面应用框架
tauri = { version = "1.0", features = ["api-all"] }

# 数据库
surrealdb = "1.0"  # 图数据库 + 文档数据库
sled = "0.34"      # 嵌入式KV存储（缓存）

# 向量数据库
qdrant-client = "1.0"  # 向量搜索

# AI集成 - 统一多模型支持
async-openai = "0.14"  # OpenAI API客户端
reqwest = { version = "0.11", features = ["json"] }
async-trait = "0.1"    # 异步trait支持

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 文本处理
tantivy = "0.19"  # 全文搜索引擎

# 工具库
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
```

## 🗄️ 数据存储方案

### 主数据库：SurrealDB
```rust
// 知识节点结构
#[derive(Serialize, Deserialize)]
struct KnowledgeNode {
    id: String,
    title: String,
    content: String,
    node_type: NodeType,
    position: Position,
    metadata: NodeMetadata,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}

// 节点关系
#[derive(Serialize, Deserialize)]
struct NodeRelation {
    id: String,
    from_node: String,
    to_node: String,
    relation_type: RelationType,
    weight: f32,
    metadata: RelationMetadata,
}
```

### 向量存储：Qdrant
```rust
// 向量化的知识节点
struct VectorizedNode {
    node_id: String,
    embedding: Vec<f32>,
    metadata: HashMap<String, String>,
}
```

### 全文搜索：Tantivy
```rust
// 搜索索引结构
struct SearchIndex {
    title: String,
    content: String,
    node_type: String,
    tags: Vec<String>,
}
```

## 🎨 前端架构设计

### 组件结构
```
src/
├── components/
│   ├── ui/                          # 基础UI组件(TailwindCSS)
│   │   ├── Button.tsx               # 按钮组件
│   │   ├── Input.tsx                # 输入框组件
│   │   ├── Modal.tsx                # 模态框组件
│   │   ├── Select.tsx               # 选择器组件
│   │   └── Tooltip.tsx              # 提示组件
│   ├── Canvas/
│   │   ├── KnowledgeCanvas.tsx      # 主画布组件
│   │   ├── NodeRenderer.tsx         # 节点渲染器
│   │   ├── EdgeRenderer.tsx         # 连线渲染器
│   │   └── CanvasControls.tsx       # 画布控制器
│   ├── Sidebar/
│   │   ├── NodeEditor.tsx           # 节点编辑器
│   │   ├── SearchPanel.tsx          # 搜索面板
│   │   ├── AIAssistant.tsx          # AI助手面板
│   │   └── ModelSelector.tsx        # AI模型选择器
│   └── Common/
│       ├── NodeCard.tsx             # 节点卡片
│       └── RelationLine.tsx         # 关系连线
├── stores/
│   ├── canvasStore.ts               # 画布状态
│   ├── knowledgeStore.ts            # 知识库状态
│   ├── aiStore.ts                   # AI交互状态
│   └── settingsStore.ts             # 设置状态(模型配置等)
├── services/
│   ├── tauriApi.ts                  # Tauri API调用
│   ├── knowledgeService.ts          # 知识库服务
│   └── aiService.ts                 # AI服务(统一接口)
├── types/
│   ├── knowledge.ts                 # 知识库类型定义
│   ├── canvas.ts                    # 画布类型定义
│   └── ai.ts                        # AI模型类型定义
└── styles/
    └── globals.css                  # TailwindCSS全局样式
```

### 核心功能设计

#### 1. 知识画布系统
**功能描述**：提供无限大的可视化画布，支持知识节点的展示、交互和管理

**核心逻辑**：
- 画布缩放和平移：支持鼠标滚轮缩放，拖拽平移
- 节点渲染：动态渲染可见区域内的节点，优化性能
- 交互处理：节点选择、拖拽、连线等操作
- 视口管理：智能计算可见区域，实现虚拟化渲染

**实现思路**：
```
画布组件架构：
├── CanvasContainer (画布容器)
│   ├── ViewportManager (视口管理器)
│   ├── InteractionHandler (交互处理器)
│   ├── RenderEngine (渲染引擎)
│   └── ControlPanel (控制面板)

渲染流程：
1. 计算当前视口范围
2. 筛选可见节点和连线
3. 批量渲染可见元素
4. 处理用户交互事件
5. 更新画布状态
```

**伪代码**：
```
class KnowledgeCanvas {
    viewport: ViewportState
    nodes: KnowledgeNode[]
    edges: NodeEdge[]

    function render() {
        visibleNodes = calculateVisibleNodes(viewport, nodes)
        visibleEdges = calculateVisibleEdges(viewport, edges)

        renderNodes(visibleNodes)
        renderEdges(visibleEdges)
        renderControls()
    }

    function handleInteraction(event) {
        switch(event.type) {
            case 'wheel': handleZoom(event)
            case 'drag': handlePan(event)
            case 'click': handleNodeSelection(event)
            case 'doubleClick': handleNodeEdit(event)
        }
    }
}
```

#### 2. 知识节点管理
**功能描述**：知识节点的创建、编辑、删除和关系管理

**核心逻辑**：
- 节点生命周期：创建 → 编辑 → 保存 → 删除
- 关系建立：拖拽连线建立节点间关系
- 属性管理：标题、内容、标签、元数据等
- 布局算法：自动布局和手动调整

**实现思路**：
```
节点管理系统：
├── NodeFactory (节点工厂)
├── RelationshipManager (关系管理器)
├── LayoutEngine (布局引擎)
└── ValidationService (验证服务)

操作流程：
1. 用户触发创建节点
2. 验证节点数据有效性
3. 生成唯一节点ID
4. 保存到数据库
5. 更新画布显示
6. 建立相关索引
```

**伪代码**：
```
class NodeManager {
    function createNode(data: NodeInput) -> KnowledgeNode {
        validate(data)
        node = NodeFactory.create(data)

        // 保存到多个存储
        database.save(node)
        vectorDB.index(node.content, node.id)
        searchIndex.add(node)

        // 触发AI分析
        aiService.analyzeNode(node)

        return node
    }

    function createRelation(fromId, toId, relationType) {
        relation = new NodeRelation(fromId, toId, relationType)

        // 验证关系有效性
        if (validateRelation(relation)) {
            database.saveRelation(relation)
            updateGraphIndex(relation)
        }
    }
}
```

## 🔧 后端服务架构

### 1. 服务层设计
**功能描述**：提供统一的业务逻辑处理和数据访问接口

**架构设计**：
```
后端服务架构：
├── API Layer (Tauri命令层)
│   ├── 知识节点管理命令
│   ├── 搜索服务命令
│   ├── AI服务命令
│   └── 配置管理命令
├── Business Layer (业务逻辑层)
│   ├── KnowledgeService (知识管理服务)
│   ├── SearchService (搜索服务)
│   ├── AIService (AI服务)
│   └── ConfigService (配置服务)
├── Data Layer (数据访问层)
│   ├── GraphRepository (图数据库访问)
│   ├── VectorRepository (向量数据库访问)
│   ├── SearchRepository (搜索索引访问)
│   └── CacheRepository (缓存访问)
└── Infrastructure Layer (基础设施层)
    ├── Database Connections
    ├── External API Clients
    └── Configuration Management
```

**核心命令接口**：
```
// 知识管理命令
create_knowledge_node(input: NodeInput) -> Result<Node>
update_knowledge_node(id: String, input: NodeInput) -> Result<Node>
delete_knowledge_node(id: String) -> Result<()>
get_knowledge_node(id: String) -> Result<Node>
create_node_relation(from: String, to: String, type: RelationType) -> Result<Relation>

// 搜索命令
full_text_search(query: String, filters: SearchFilters) -> Result<SearchResults>
semantic_search(query: String, limit: u32) -> Result<SearchResults>
graph_search(start_node: String, depth: u32) -> Result<GraphResults>

// AI服务命令
ai_expand_idea(idea: String, context: Option<String>) -> Result<Vec<Node>>
ai_suggest_relations(node_id: String) -> Result<Vec<RelationSuggestion>>
ai_summarize_content(content: String) -> Result<String>
```

### 2. 知识库服务逻辑
**功能描述**：管理知识节点的完整生命周期和关系网络

**核心逻辑流程**：
```
节点创建流程：
1. 数据验证 → 2. 生成唯一ID → 3. 保存到图数据库
4. 内容向量化 → 5. 存储到向量数据库 → 6. 更新搜索索引
7. 触发AI分析 → 8. 返回创建结果

关系建立流程：
1. 验证节点存在性 → 2. 检查关系合法性 → 3. 保存关系数据
4. 更新图索引 → 5. 计算关系权重 → 6. 触发相关推荐
```

**伪代码**：
```
class KnowledgeService {
    graphDB: GraphDatabase
    vectorDB: VectorDatabase
    searchIndex: SearchIndex
    aiService: AIService

    function createNode(input: NodeInput) -> Result<Node> {
        // 1. 数据验证
        validate(input)

        // 2. 创建节点对象
        node = Node.new(
            id: generateUUID(),
            title: input.title,
            content: input.content,
            position: input.position,
            timestamp: now()
        )

        // 3. 多存储保存
        transaction {
            graphDB.saveNode(node)

            if (node.content.notEmpty()) {
                embedding = aiService.generateEmbedding(node.content)
                vectorDB.storeVector(node.id, embedding)
            }

            searchIndex.addDocument(node)
        }

        // 4. 异步AI分析
        async {
            suggestions = aiService.analyzeSimilarNodes(node)
            cacheRelationSuggestions(node.id, suggestions)
        }

        return node
    }

    function semanticSearch(query: String) -> Result<SearchResults> {
        // 1. 查询向量化
        queryVector = aiService.generateEmbedding(query)

        // 2. 向量相似性搜索
        similarVectors = vectorDB.searchSimilar(queryVector, limit: 20)

        // 3. 获取完整节点信息
        results = []
        for vector in similarVectors {
            node = graphDB.getNode(vector.nodeId)
            results.add(SearchResult {
                node: node,
                score: vector.similarity,
                type: "semantic"
            })
        }

        return results
    }
}
```

## 🤖 AI 统一集成方案

### 1. AI 抽象层设计
**功能描述**：为不同AI提供商提供统一的接口，屏蔽底层实现差异

**设计原则**：
- **统一接口**：所有AI提供商实现相同的服务接口
- **配置驱动**：通过配置文件管理不同模型的参数
- **错误统一**：统一的错误处理和重试机制
- **扩展性**：易于添加新的AI提供商

**架构设计**：
```
AI服务架构：
├── AIManager (AI管理器)
│   ├── 提供商注册和管理
│   ├── 配置管理和验证
│   ├── 请求路由和负载均衡
│   └── 错误处理和重试
├── Provider Implementations (提供商实现)
│   ├── OpenAIProvider
│   ├── ClaudeProvider
│   ├── GeminiProvider
│   ├── QianWenProvider
│   ├── ChatGLMProvider
│   ├── BaiduProvider
│   └── LocalProvider
└── Common Services (通用服务)
    ├── TokenManager (令牌管理)
    ├── RateLimiter (限流器)
    ├── CacheManager (缓存管理)
    └── MetricsCollector (指标收集)
```

**核心接口定义**：
```
trait AIService {
    // 聊天完成
    async fn chatCompletion(messages: Vec<Message>, config: ModelConfig) -> Result<Response>

    // 文本嵌入
    async fn generateEmbedding(text: String, config: ModelConfig) -> Result<Vec<f32>>

    // 流式响应
    async fn streamCompletion(messages: Vec<Message>, config: ModelConfig) -> Result<Stream<String>>

    // 功能检查
    fn supportsStreaming() -> bool
    fn supportsEmbedding() -> bool
    fn supportsFunctions() -> bool

    // 模型信息
    fn getAvailableModels() -> Vec<ModelInfo>
    fn getModelLimits(modelName: String) -> ModelLimits
}
```

### 2. 多提供商适配策略
**功能描述**：处理不同AI提供商的API差异，提供统一的调用体验

**适配逻辑**：
```
请求处理流程：
1. 请求预处理 → 2. 提供商选择 → 3. 参数转换
4. API调用 → 5. 响应标准化 → 6. 错误处理

参数映射策略：
- 消息格式统一：role + content 标准格式
- 参数名称映射：temperature, max_tokens, top_p 等
- 响应格式统一：content, usage, model 标准字段
- 错误码映射：统一的错误类型和错误码
```

**伪代码**：
```
class AIManager {
    providers: Map<ProviderType, AIProvider>
    configs: Map<ProviderType, ProviderConfig>

    function expandIdea(idea: String, context: String, providerType: ProviderType) -> Result<Vec<Node>> {
        // 1. 获取提供商和配置
        provider = providers.get(providerType)
        config = configs.get(providerType)

        // 2. 构建提示词
        prompt = buildExpansionPrompt(idea, context)
        messages = [
            Message { role: "system", content: SYSTEM_PROMPT },
            Message { role: "user", content: prompt }
        ]

        // 3. 调用AI服务
        response = provider.chatCompletion(messages, config)

        // 4. 解析响应
        nodes = parseAIResponse(response.content)

        // 5. 验证和后处理
        validatedNodes = validateNodes(nodes)

        return validatedNodes
    }

    function adaptRequest(request: StandardRequest, providerType: ProviderType) -> ProviderRequest {
        switch providerType {
            case OpenAI:
                return OpenAIRequest {
                    model: request.model,
                    messages: request.messages,
                    temperature: request.temperature,
                    max_tokens: request.maxTokens
                }
            case Claude:
                return ClaudeRequest {
                    model: request.model,
                    messages: adaptMessagesForClaude(request.messages),
                    temperature: request.temperature,
                    max_tokens_to_sample: request.maxTokens
                }
            // ... 其他提供商适配
        }
    }
}
```

### 3. 智能路由和负载均衡
**功能描述**：根据模型能力、成本、响应时间等因素智能选择最优的AI提供商

**路由策略**：
```
路由决策因素：
├── 功能需求匹配
│   ├── 是否支持所需功能
│   ├── 模型能力评估
│   └── 输出质量评分
├── 成本考虑
│   ├── Token价格对比
│   ├── 请求频率限制
│   └── 配额使用情况
├── 性能指标
│   ├── 响应时间统计
│   ├── 成功率统计
│   └── 错误率统计
└── 用户偏好
    ├── 默认提供商设置
    ├── 任务类型偏好
    └── 质量要求设置
```

**伪代码**：
```
class AIRouter {
    function selectProvider(request: AIRequest) -> ProviderType {
        candidates = getAvailableProviders(request.requiredFeatures)

        scores = []
        for provider in candidates {
            score = calculateScore(provider, request)
            scores.add(ProviderScore { provider, score })
        }

        // 按分数排序，选择最优提供商
        bestProvider = scores.sortByScore().first()

        return bestProvider.provider
    }

    function calculateScore(provider: ProviderType, request: AIRequest) -> f32 {
        // 功能匹配分数 (40%)
        featureScore = calculateFeatureScore(provider, request) * 0.4

        // 成本分数 (30%)
        costScore = calculateCostScore(provider, request) * 0.3

        // 性能分数 (20%)
        performanceScore = calculatePerformanceScore(provider) * 0.2

        // 用户偏好分数 (10%)
        preferenceScore = calculatePreferenceScore(provider, request.userId) * 0.1

        return featureScore + costScore + performanceScore + preferenceScore
    }
}

### 各大模型厂商实现
```rust
// src-tauri/src/services/ai/providers/openai.rs
use super::*;
use async_openai::{Client, types::*};

pub struct OpenAIService {
    client: Client,
}

#[async_trait]
impl AIService for OpenAIService {
    async fn chat_completion(
        &self,
        messages: Vec<AIMessage>,
        config: &AIModelConfig,
    ) -> Result<AIResponse, AIError> {
        let openai_messages: Vec<ChatCompletionRequestMessage> = messages
            .into_iter()
            .map(|msg| match msg.role.as_str() {
                "system" => ChatCompletionRequestSystemMessageArgs::default()
                    .content(msg.content)
                    .build()
                    .unwrap()
                    .into(),
                "user" => ChatCompletionRequestUserMessageArgs::default()
                    .content(msg.content)
                    .build()
                    .unwrap()
                    .into(),
                _ => ChatCompletionRequestAssistantMessageArgs::default()
                    .content(msg.content)
                    .build()
                    .unwrap()
                    .into(),
            })
            .collect();

        let request = CreateChatCompletionRequestArgs::default()
            .model(&config.model_name)
            .messages(openai_messages)
            .max_tokens(config.max_tokens.unwrap_or(1000))
            .temperature(config.temperature.unwrap_or(0.7))
            .build()
            .map_err(|e| AIError::ConfigError(e.to_string()))?;

        let response = self.client
            .chat()
            .completions()
            .create(request)
            .await
            .map_err(|e| AIError::ApiError(e.to_string()))?;

        if let Some(choice) = response.choices.first() {
            if let Some(content) = &choice.message.content {
                return Ok(AIResponse {
                    content: content.clone(),
                    usage: response.usage.map(|u| AIUsage {
                        prompt_tokens: u.prompt_tokens,
                        completion_tokens: u.completion_tokens,
                        total_tokens: u.total_tokens,
                    }),
                    model: response.model,
                });
            }
        }

        Err(AIError::ApiError("No response content".to_string()))
    }

    async fn generate_embedding(
        &self,
        text: &str,
        config: &AIModelConfig,
    ) -> Result<Vec<f32>, AIError> {
        let request = CreateEmbeddingRequestArgs::default()
            .model("text-embedding-ada-002")
            .input(text)
            .build()
            .map_err(|e| AIError::ConfigError(e.to_string()))?;

        let response = self.client
            .embeddings()
            .create(request)
            .await
            .map_err(|e| AIError::ApiError(e.to_string()))?;

        if let Some(embedding) = response.data.first() {
            Ok(embedding.embedding.clone())
        } else {
            Err(AIError::ApiError("No embedding data".to_string()))
        }
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    async fn chat_completion_stream(
        &self,
        messages: Vec<AIMessage>,
        config: &AIModelConfig,
    ) -> Result<Box<dyn Stream<Item = Result<String, AIError>>>, AIError> {
        // 实现流式响应
        todo!("实现OpenAI流式响应")
    }
}
```

### 统一AI管理器
```rust
// src-tauri/src/services/ai/manager.rs
use std::collections::HashMap;
use std::sync::Arc;

pub struct AIManager {
    providers: HashMap<AIProvider, Arc<dyn AIService>>,
    default_config: AIModelConfig,
}

impl AIManager {
    pub fn new() -> Self {
        let mut providers: HashMap<AIProvider, Arc<dyn AIService>> = HashMap::new();

        // 注册各个提供商
        providers.insert(AIProvider::OpenAI, Arc::new(OpenAIService::new()));
        providers.insert(AIProvider::Claude, Arc::new(ClaudeService::new()));
        providers.insert(AIProvider::Gemini, Arc::new(GeminiService::new()));
        providers.insert(AIProvider::QianWen, Arc::new(QianWenService::new()));
        providers.insert(AIProvider::ChatGLM, Arc::new(ChatGLMService::new()));
        providers.insert(AIProvider::Baidu, Arc::new(BaiduService::new()));

        Self {
            providers,
            default_config: AIModelConfig {
                provider: AIProvider::OpenAI,
                model_name: "gpt-3.5-turbo".to_string(),
                api_key: String::new(),
                base_url: None,
                max_tokens: Some(1000),
                temperature: Some(0.7),
            },
        }
    }

    pub async fn expand_idea(
        &self,
        idea: &str,
        context: Option<&str>,
        config: Option<&AIModelConfig>,
    ) -> Result<Vec<KnowledgeNode>, AIError> {
        let config = config.unwrap_or(&self.default_config);
        let service = self.providers
            .get(&config.provider)
            .ok_or_else(|| AIError::ConfigError("Unsupported provider".to_string()))?;

        let prompt = self.build_expansion_prompt(idea, context);
        let messages = vec![
            AIMessage {
                role: "system".to_string(),
                content: "你是一个知识管理助手，帮助用户扩展和组织想法。".to_string(),
            },
            AIMessage {
                role: "user".to_string(),
                content: prompt,
            },
        ];

        let response = service.chat_completion(messages, config).await?;
        self.parse_ai_response(&response.content).await
    }

    pub async fn get_available_models(&self, provider: AIProvider) -> Vec<String> {
        match provider {
            AIProvider::OpenAI => vec![
                "gpt-4".to_string(),
                "gpt-4-turbo".to_string(),
                "gpt-3.5-turbo".to_string(),
            ],
            AIProvider::Claude => vec![
                "claude-3-opus".to_string(),
                "claude-3-sonnet".to_string(),
                "claude-3-haiku".to_string(),
            ],
            AIProvider::Gemini => vec![
                "gemini-pro".to_string(),
                "gemini-pro-vision".to_string(),
            ],
            AIProvider::QianWen => vec![
                "qwen-turbo".to_string(),
                "qwen-plus".to_string(),
                "qwen-max".to_string(),
            ],
            AIProvider::ChatGLM => vec![
                "glm-4".to_string(),
                "glm-3-turbo".to_string(),
            ],
            AIProvider::Baidu => vec![
                "ernie-bot".to_string(),
                "ernie-bot-turbo".to_string(),
            ],
            AIProvider::Local => vec![
                "llama2".to_string(),
                "mistral".to_string(),
            ],
        }
    }

    fn build_expansion_prompt(&self, idea: &str, context: Option<&str>) -> String {
        let context_part = context
            .map(|c| format!("现有上下文：{}\n\n", c))
            .unwrap_or_default();

        format!(
            "{}基于这个想法：'{}'\n\n请生成3-5个相关的知识点，每个知识点包含：\n\
            1. 标题（简洁明了）\n\
            2. 详细内容（100-200字）\n\
            3. 与原想法的关系类型\n\n\
            请以JSON格式返回，格式如下：\n\
            {{\n\
              \"nodes\": [\n\
                {{\n\
                  \"title\": \"知识点标题\",\n\
                  \"content\": \"详细内容\",\n\
                  \"relation_type\": \"extends|supports|contradicts|example\"\n\
                }}\n\
              ]\n\
            }}",
            context_part, idea
        )
    }

    async fn parse_ai_response(&self, content: &str) -> Result<Vec<KnowledgeNode>, AIError> {
        // 解析AI响应并转换为知识节点
        // 实现JSON解析逻辑
        todo!("实现AI响应解析")
    }
}
```

### 4. 前端AI配置管理
**功能描述**：提供用户友好的AI模型配置和管理界面

**核心功能**：
- **提供商管理**：支持多个AI提供商的配置和切换
- **模型选择**：动态加载可用模型列表
- **参数配置**：温度、Token限制、API密钥等参数设置
- **状态持久化**：配置信息本地存储和同步

**界面设计逻辑**：
```
AI配置界面结构：
├── 提供商选择器
│   ├── 下拉列表显示可用提供商
│   ├── 提供商图标和名称
│   └── 配置状态指示器
├── 模型配置面板
│   ├── 模型选择下拉框
│   ├── API密钥输入框
│   ├── 基础URL配置（可选）
│   └── 高级参数设置
├── 参数调节器
│   ├── Temperature滑块 (0-2)
│   ├── Max Tokens输入框
│   ├── Top-p滑块（可选）
│   └── 其他模型特定参数
└── 操作按钮
    ├── 保存配置
    ├── 测试连接
    ├── 重置默认值
    └── 导入/导出配置
```

**状态管理逻辑**：
```
class AIConfigManager {
    state: {
        availableProviders: Provider[]
        currentProvider: string
        providerConfigs: Map<string, ProviderConfig>
        isConfiguring: boolean
        testResults: Map<string, TestResult>
    }

    function loadAvailableProviders() {
        // 从后端获取支持的提供商列表
        providers = await invoke('get_available_providers')

        // 更新状态
        setState({
            availableProviders: providers,
            currentProvider: providers[0]?.id || ''
        })
    }

    function switchProvider(providerId: string) {
        // 切换当前提供商
        setState({ currentProvider: providerId })

        // 加载该提供商的可用模型
        models = await invoke('get_provider_models', { providerId })
        updateProviderModels(providerId, models)
    }

    function saveConfiguration(providerId: string, config: ProviderConfig) {
        // 验证配置有效性
        if (validateConfig(config)) {
            // 保存到本地存储
            localStorage.setItem(`ai_config_${providerId}`, JSON.stringify(config))

            // 更新状态
            setState({
                providerConfigs: {
                    ...state.providerConfigs,
                    [providerId]: config
                }
            })

            // 通知后端更新配置
            invoke('update_ai_config', { providerId, config })
        }
    }

    function testConnection(providerId: string, config: ProviderConfig) {
        setState({ isConfiguring: true })

        try {
            result = await invoke('test_ai_connection', { providerId, config })

            setState({
                testResults: {
                    ...state.testResults,
                    [providerId]: {
                        success: true,
                        latency: result.latency,
                        model: result.model
                    }
                }
            })
        } catch (error) {
            setState({
                testResults: {
                    ...state.testResults,
                    [providerId]: {
                        success: false,
                        error: error.message
                    }
                }
            })
        } finally {
            setState({ isConfiguring: false })
        }
    }
}
```

### 5. 智能提示词管理
**功能描述**：管理和优化不同场景下的AI提示词模板

**提示词分类**：
```
提示词模板体系：
├── 知识扩展类
│   ├── 想法扩展模板
│   ├── 概念解释模板
│   ├── 关系发现模板
│   └── 深度分析模板
├── 内容生成类
│   ├── 摘要生成模板
│   ├── 标题优化模板
│   ├── 标签提取模板
│   └── 分类建议模板
├── 搜索优化类
│   ├── 查询扩展模板
│   ├── 语义理解模板
│   ├── 意图识别模板
│   └── 结果排序模板
└── 质量评估类
    ├── 内容评分模板
    ├── 相关性判断模板
    ├── 完整性检查模板
    └── 准确性验证模板
```

**动态提示词生成**：
```
class PromptManager {
    templates: Map<string, PromptTemplate>

    function generateExpansionPrompt(idea: string, context: Context) -> string {
        template = templates.get("idea_expansion")

        // 根据上下文调整提示词
        adjustedTemplate = adjustPromptForContext(template, context)

        // 填充变量
        prompt = fillTemplate(adjustedTemplate, {
            idea: idea,
            existingNodes: context.relatedNodes,
            userPreferences: context.userPrefs,
            domainKnowledge: context.domain
        })

        return prompt
    }

    function optimizePromptForProvider(prompt: string, provider: ProviderType) -> string {
        switch provider {
            case OpenAI:
                return optimizeForOpenAI(prompt)
            case Claude:
                return optimizeForClaude(prompt)
            case Gemini:
                return optimizeForGemini(prompt)
            // ... 其他提供商优化
        }
    }
}

### 6. 搜索系统设计
**功能描述**：提供多维度的知识检索能力，包括全文搜索、语义搜索和图搜索

**搜索架构**：
```
搜索系统架构：
├── 搜索接口层
│   ├── 统一搜索入口
│   ├── 搜索类型路由
│   ├── 结果聚合器
│   └── 搜索建议生成
├── 搜索引擎层
│   ├── 全文搜索引擎 (Tantivy)
│   ├── 向量搜索引擎 (Qdrant)
│   ├── 图搜索引擎 (SurrealDB)
│   └── 混合搜索协调器
├── 索引管理层
│   ├── 文本索引管理
│   ├── 向量索引管理
│   ├── 图索引管理
│   └── 索引同步服务
└── 搜索优化层
    ├── 查询理解和扩展
    ├── 个性化排序
    ├── 搜索结果缓存
    └── 搜索分析统计
```

**搜索逻辑流程**：
```
智能搜索流程：
1. 查询预处理 → 2. 搜索意图识别 → 3. 多引擎并行搜索
4. 结果去重合并 → 5. 相关性排序 → 6. 个性化调整
7. 结果展示 → 8. 用户反馈收集 → 9. 搜索优化

伪代码：
class SearchService {
    function intelligentSearch(query: string, context: SearchContext) -> SearchResults {
        // 1. 查询理解
        intent = analyzeSearchIntent(query)
        expandedQuery = expandQuery(query, context)

        // 2. 并行搜索
        results = []

        if (intent.needsFullText) {
            fullTextResults = fullTextSearch(expandedQuery)
            results.add(fullTextResults)
        }

        if (intent.needsSemantic) {
            semanticResults = semanticSearch(query)
            results.add(semanticResults)
        }

        if (intent.needsGraph) {
            graphResults = graphSearch(query, context.currentNode)
            results.add(graphResults)
        }

        // 3. 结果融合和排序
        mergedResults = mergeAndDedup(results)
        rankedResults = rankResults(mergedResults, context)

        return rankedResults
    }
}
```

### 7. 数据同步和一致性
**功能描述**：确保多个存储系统间的数据一致性和同步

**同步策略**：
```
数据一致性保证：
├── 事务管理
│   ├── 分布式事务协调
│   ├── 补偿事务处理
│   ├── 事务日志记录
│   └── 失败回滚机制
├── 数据同步
│   ├── 实时同步 (Change Data Capture)
│   ├── 批量同步 (定时任务)
│   ├── 增量同步 (Delta Updates)
│   └── 冲突解决策略
├── 一致性检查
│   ├── 数据完整性验证
│   ├── 跨库一致性检查
│   ├── 索引一致性验证
│   └── 自动修复机制
└── 监控告警
    ├── 同步延迟监控
    ├── 数据不一致告警
    ├── 性能指标监控
    └── 错误日志分析
```

**同步实现逻辑**：
```
class DataSyncService {
    function syncNodeUpdate(node: KnowledgeNode) {
        transaction {
            // 1. 更新主数据库
            graphDB.updateNode(node)

            // 2. 更新向量数据库
            if (node.contentChanged) {
                embedding = aiService.generateEmbedding(node.content)
                vectorDB.updateVector(node.id, embedding)
            }

            // 3. 更新搜索索引
            searchIndex.updateDocument(node)

            // 4. 更新缓存
            cache.invalidate(node.id)
            cache.set(node.id, node)

            // 5. 记录变更日志
            changeLog.record(ChangeEvent {
                type: "node_update",
                nodeId: node.id,
                timestamp: now(),
                changes: calculateChanges(node)
            })
        }
    }

    function handleSyncFailure(operation: SyncOperation, error: Error) {
        // 记录失败
        failureLog.record(operation, error)

        // 重试策略
        if (shouldRetry(operation, error)) {
            scheduleRetry(operation, calculateBackoff(operation.retryCount))
        } else {
            // 标记为需要手动处理
            manualInterventionQueue.add(operation)
            alertAdmin(operation, error)
        }
    }
}
```

## 📦 项目结构

```
knowledge-management-system/
├── src-tauri/                    # Rust 后端
│   ├── src/
│   │   ├── main.rs              # 主入口
│   │   ├── commands/            # Tauri 命令
│   │   ├── services/            # 业务服务
│   │   ├── models/              # 数据模型
│   │   └── utils/               # 工具函数
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                         # React 前端
│   ├── components/              # 组件
│   ├── stores/                  # 状态管理
│   ├── services/                # 前端服务
│   ├── types/                   # 类型定义
│   ├── styles/                  # 样式文件
│   └── main.tsx                 # 前端入口
├── public/                      # 静态资源
├── package.json
├── vite.config.ts
├── tailwind.config.js
├── postcss.config.js
└── README.md

## 📋 技术实现要点

### 1. 前端技术栈配置
**核心依赖**：
- **React 18 + TypeScript**：现代化前端框架和类型安全
- **TailwindCSS**：实用优先的CSS框架，快速构建UI
- **Vite**：快速的构建工具和开发服务器
- **Konva.js**：高性能2D Canvas库，用于画布渲染
- **Zustand**：轻量级状态管理库
- **Tauri API**：前后端通信接口

**开发工具配置**：
- **TypeScript配置**：严格模式，路径映射，类型检查
- **TailwindCSS配置**：自定义主题，组件类，响应式设计
- **Vite配置**：开发服务器，构建优化，Tauri集成
- **ESLint + Prettier**：代码规范和格式化

### 2. 后端技术栈配置
**核心依赖**：
- **Tauri**：跨平台桌面应用框架
- **SurrealDB**：现代多模型数据库（图+文档）
- **Qdrant**：高性能向量数据库
- **Tantivy**：全文搜索引擎
- **Tokio**：异步运行时
- **Serde**：序列化框架

**AI集成依赖**：
- **async-openai**：OpenAI API客户端
- **reqwest**：HTTP客户端库
- **async-trait**：异步trait支持
- **自定义适配器**：其他AI提供商的适配实现
```

## 🚀 开发计划

### 第一阶段（2-3周）：基础框架搭建
- [ ] 搭建 Tauri + React + TypeScript + TailwindCSS 项目结构
- [ ] 配置开发环境和构建流程
- [ ] 集成 SurrealDB 数据库
- [ ] 实现基础的节点 CRUD 操作
- [ ] 搭建基础画布组件（Konva.js）
- [ ] 创建基础 UI 组件库（TailwindCSS）

### 第二阶段（3-4周）：核心功能实现
- [ ] 实现可视化知识图谱展示
- [ ] 添加节点关系管理和连线功能
- [ ] 集成 Qdrant 向量数据库
- [ ] 实现 Tantivy 全文搜索功能
- [ ] 构建 AI 模型统一抽象层
- [ ] 实现多个主流 AI 提供商支持（OpenAI、Claude、千问等）
- [ ] 开发 AI 模型配置和选择界面
- [ ] 实现基础 AI 想法扩展功能

### 第三阶段（2-3周）：AI 增强和优化
- [ ] 完善 AI 响应解析和知识节点生成
- [ ] 实现语义搜索功能
- [ ] 添加 AI 流式响应支持
- [ ] 优化画布性能（大规模节点渲染）
- [ ] 实现知识库导入导出功能
- [ ] 添加快捷键和用户体验优化

### 第四阶段（1-2周）：测试和发布准备
- [ ] 完善错误处理和日志系统
- [ ] 编写单元测试和集成测试
- [ ] 性能测试和优化
- [ ] 编写用户文档和开发文档
- [ ] 准备发布包和安装程序

## 💡 技术亮点

1. **现代化前端技术栈**：React + TypeScript + TailwindCSS，开发效率高
2. **统一AI模型接口**：支持多个主流AI提供商，易于扩展新模型
3. **高性能渲染**：Rust 后端 + Konva.js Canvas 渲染，支持大规模知识图谱
4. **模块化设计**：清晰的架构分层，便于维护和扩展
5. **跨平台支持**：一套代码支持 Windows/macOS/Linux
6. **离线优先**：本地数据库存储，支持离线使用
7. **响应式设计**：TailwindCSS 确保良好的用户体验

## 🎯 AI 模型支持策略

### 已规划支持的模型
- **OpenAI**: GPT-4, GPT-3.5-turbo
- **Anthropic**: Claude-3 系列
- **Google**: Gemini Pro 系列
- **阿里云**: 千问系列
- **智谱AI**: ChatGLM 系列
- **百度**: 文心一言系列
- **本地模型**: Llama2, Mistral 等

### 扩展新模型的步骤
1. 实现对应的 `AIService` trait
2. 在 `AIManager` 中注册新提供商
3. 更新前端模型列表配置
4. 添加相应的配置界面

## 🔍 风险评估与应对

### 技术风险
- **中等 - Tauri 生态**：相对较新，可能遇到兼容性问题
  - *应对*：提前技术验证，准备 Electron 备选方案
- **低 - 开源库选择**：选用成熟稳定的库
  - *应对*：持续关注库的更新和社区活跃度

### 开发风险
- **低 - 前端技术栈**：React + TypeScript 成熟稳定
- **中等 - AI 集成复杂度**：多模型适配需要大量测试
  - *应对*：分阶段实现，先支持主流模型
- **中等 - 性能优化**：大规模图谱渲染可能遇到性能瓶颈
  - *应对*：实现虚拟化渲染，按需加载节点

### 业务风险
- **低 - 用户接受度**：知识管理是刚需
- **中等 - AI API 成本**：频繁调用可能产生较高费用
  - *应对*：实现本地缓存，支持本地模型

### 解决方案
- **技术验证优先**：核心技术提前验证可行性
- **分阶段开发**：按功能模块逐步实现，降低风险
- **持续集成**：建立自动化测试和部署流程
- **用户反馈**：早期发布 MVP 版本收集用户反馈
